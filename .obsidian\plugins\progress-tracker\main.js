/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var $=Object.defineProperty;var P=Object.getOwnPropertyDescriptor;var F=Object.getOwnPropertyNames;var E=Object.prototype.hasOwnProperty;var A=(C,f)=>{for(var t in f)$(C,t,{get:f[t],enumerable:!0})},L=(C,f,t,n)=>{if(f&&typeof f=="object"||typeof f=="function")for(let e of F(f))!E.call(C,e)&&e!==t&&$(C,e,{get:()=>f[e],enumerable:!(n=P(f,e))||n.enumerable});return C};var N=C=>L($({},"__esModule",{value:!0}),C);var B={};A(B,{default:()=>x});module.exports=N(B);var c=require("obsidian"),S=class{constructor(f){this.isDebugEnabled=f}log(f,...t){this.isDebugEnabled()&&console.log(`[Progress Tracker] ${f}`,...t)}error(f,t){this.isDebugEnabled()&&console.error(`[Progress Tracker ERROR] ${f}`,t)}warn(f,...t){this.isDebugEnabled()&&console.warn(`[Progress Tracker WARNING] ${f}`,...t)}};function K(C){var f,t,n;try{if(typeof window!="undefined"&&window.DataviewAPI)return window.DataviewAPI;let e=C,s=(t=(f=e.plugins)==null?void 0:f.plugins)==null?void 0:t.dataview;if(s!=null&&s.api)return s.api;let o=(n=e.plugins)==null?void 0:n.enabledPlugins;return o!=null&&o.has("dataview"),null}catch(e){return console.error("Error accessing Dataview API:",e),null}}var M={showDebugInfo:!1,progressColorScheme:"default",lowProgressColor:"#e06c75",mediumProgressColor:"#e5c07b",highProgressColor:"#61afef",completeProgressColor:"#98c379",lowProgressThreshold:33,mediumProgressThreshold:66,highProgressThreshold:99,showUpdateAnimation:!0,updateAnimationDelay:150,editorChangeDelay:200,keyboardInputDelay:50,checkboxClickDelay:100,maxTabsHeight:"auto",autoUpdateMetadata:!0,autoChangeStatus:!0,autoUpdateFinishedDate:!0,autoUpdateKanban:!0,kanbanCompletedColumn:"Complete",statusTodo:"Todo",statusInProgress:"In Progress",statusCompleted:"Completed",kanbanAutoDetect:!0,kanbanSpecificFiles:[],kanbanExcludeFiles:[],kanbanSyncWithStatus:!0,autoAddToKanban:!1,autoAddKanbanBoard:"",autoAddKanbanColumn:"Todo",enableCustomCheckboxStates:!1,kanbanColumnCheckboxMappings:[{columnName:"Todo",checkboxState:"[ ]"},{columnName:"In Progress",checkboxState:"[/]"},{columnName:"Complete",checkboxState:"[x]"},{columnName:"Done",checkboxState:"[x]"}],enableKanbanToFileSync:!1,enableKanbanAutoSync:!1,enableKanbanNormalizationProtection:!0},x=class extends c.Plugin{constructor(){super(...arguments);this.dvAPI=null;this.sidebarView=null;this.lastActiveFile=null;this.lastFileContent="";this.dataviewCheckInterval=null;this.lastKanbanContent=new Map;this.isUpdatingFromKanban=!1;this.autoSyncedFiles=new Set;this.lastFileUpdateMap=new Map;this.kanbanNormalizationDetector=new Map;this.fileOperationLimiter=new Map;this.FILE_OPERATION_DELAY=100}async onload(){await this.loadSettings(),this.logger=new S(()=>this.settings.showDebugInfo),this.applyMaxTabsHeightStyle(),this.registerView("progress-tracker",t=>this.sidebarView=new T(t,this)),this.addRibbonIcon("bar-chart-horizontal","Progress Tracker",()=>{this.activateView()}),this.addSettingTab(new D(this.app,this)),this.checkDataviewAPI(),this.registerEvent(this.app.workspace.on("file-open",t=>{t&&(this.lastActiveFile=t,this.settings.enableKanbanAutoSync&&this.settings.enableCustomCheckboxStates&&this.isKanbanBoard(t)&&!this.autoSyncedFiles.has(t.path)&&!this.isUpdatingFromKanban&&(this.logger.log(`Auto-syncing Kanban board on open: ${t.path}`),setTimeout(async()=>{!this.isUpdatingFromKanban&&!this.lastKanbanContent.has(t.path)?await this.autoSyncKanbanCheckboxStates(t):this.logger.log("Skipping auto-sync - update in progress or file already tracked")},800)),setTimeout(async()=>{await this.updateLastFileContent(t),this.sidebarView&&this.sidebarView.updateProgressBar(t)},100))})),this.registerEvent(this.app.vault.on("modify",async t=>{if(t instanceof c.TFile&&this.settings.enableKanbanToFileSync&&this.settings.enableCustomCheckboxStates&&this.isKanbanBoard(t)){if(this.logger.log(`File modified event for Kanban board: ${t.path}`),this.isUpdatingFromKanban){this.logger.log("Skipping file modify - currently updating from plugin");return}setTimeout(async()=>{try{let n=await this.app.vault.read(t);await this.handleKanbanBoardChange(t,n)}catch(n){this.settings.showDebugInfo&&console.error("Error handling file modify for Kanban board:",n)}},100)}})),this.registerEvent(this.app.workspace.on("editor-change",(0,c.debounce)(async(t,n)=>{if(n instanceof c.MarkdownView&&this.sidebarView){if(this.isUpdatingFromKanban){this.logger.log("Skipping editor-change - currently updating from Kanban");return}let e=t.getValue(),s=n.file;if(this.settings.enableKanbanToFileSync&&this.settings.enableCustomCheckboxStates&&s&&this.isKanbanBoard(s)){if(this.logger.log(`Detected Kanban board change: ${s.path}`),this.logger.log(`Content length: ${e.length}, lastFileContent length: ${this.lastFileContent.length}`),this.logger.log(`isUpdatingFromKanban: ${this.isUpdatingFromKanban}`),this.settings.enableKanbanNormalizationProtection){let o=this.detectImmediateKanbanNormalization(this.lastFileContent,e);if(this.logger.log(`Immediate normalization check: ${o}`),o){this.logger.log("Detected immediate Kanban normalization - reverting unwanted changes");let a=this.revertKanbanNormalization(this.lastFileContent,e,s);if(a!==e){this.isUpdatingFromKanban=!0,setTimeout(async()=>{let i=await this.syncAllCheckboxStatesToMappings(s,a);await this.app.vault.modify(s,i),this.lastFileContent=i,await this.forceRefreshKanbanUI(s),setTimeout(()=>{this.isUpdatingFromKanban=!1},100)},50);return}}else{let a=this.lastFileContent.split(`
`),i=e.split(`
`),l=0;for(let r=0;r<Math.min(a.length,i.length);r++){let h=a[r].match(/^(\s*- )\[([^\]]*)\]/),g=i[r].match(/^(\s*- )\[([^\]]*)\]/);h&&g&&h[2]!==g[2]&&(l++,this.logger.log(`Line ${r} checkbox change: [${h[2]}] \u2192 [${g[2]}]`))}l>0&&this.logger.log(`Found ${l} checkbox changes but no immediate normalization detected`)}}setTimeout(async()=>{let o=await this.app.vault.read(s);await this.handleKanbanBoardChange(s,o)},200)}(e.includes("- [")||this.lastFileContent.includes("- [")||/- \[[^\]]*\]/.test(e)||/- \[[^\]]*\]/.test(this.lastFileContent))&&(this.hasTaskContentChanged(this.lastFileContent,e)?this.lastActiveFile&&(this.lastActiveFile=n.file,this.sidebarView.updateProgressBar(n.file,e),this.lastFileContent=e):this.logger.log("Skipping update - no task changes detected"))}},this.settings.editorChangeDelay))),this.registerDomEvent(document,"keydown",t=>{let n=this.app.workspace.getActiveViewOfType(c.MarkdownView);n&&n.getMode()==="source"&&["Enter","Space","]","x","X","Backspace","Delete"].includes(t.key)&&setTimeout(()=>{let e=n.editor.getValue();(e.includes("- [")||/- \[[^\]]*\]/.test(e))&&this.hasTaskContentChanged(this.lastFileContent,e)&&(this.lastActiveFile=n.file,this.sidebarView&&this.sidebarView.updateProgressBar(n.file,e),this.lastFileContent=e)},this.settings.keyboardInputDelay)}),this.registerDomEvent(document,"click",t=>{let n=t.target;n&&n.tagName==="INPUT"&&n.classList.contains("task-list-item-checkbox")&&setTimeout(async()=>{let e=this.app.workspace.getActiveFile();if(e&&this.sidebarView){let s=await this.app.vault.read(e);this.hasTaskContentChanged(this.lastFileContent,s)&&(this.lastActiveFile=e,this.sidebarView.updateProgressBar(e,s),this.lastFileContent=s)}},this.settings.checkboxClickDelay)}),setTimeout(()=>{this.activateView(),setTimeout(async()=>{let t=this.app.workspace.getActiveFile();t&&this.sidebarView&&(this.logger.log("Initial file load after plugin start:",t.path),await this.updateLastFileContent(t),this.sidebarView.updateProgressBar(t,void 0,!0))},1500)},1e3),this.addCommand({id:"clear-completed-files-cache",name:"Clear completed files cache",callback:()=>{this.sidebarView&&(this.sidebarView.clearCompletedFilesCache(),new c.Notice("Completed files cache cleared. Files can trigger completion notifications again."))}}),this.addCommand({id:"debug-kanban-sync",name:"Debug Kanban sync status",callback:()=>{let t=this.app.workspace.getActiveFile();if(!t){new c.Notice("No active file");return}let n={"Current file":t.path,enableKanbanToFileSync:this.settings.enableKanbanToFileSync,enableCustomCheckboxStates:this.settings.enableCustomCheckboxStates,enableKanbanAutoSync:this.settings.enableKanbanAutoSync,enableKanbanNormalizationProtection:this.settings.enableKanbanNormalizationProtection,isKanbanBoard:this.isKanbanBoard(t),"Checkbox mappings":this.settings.kanbanColumnCheckboxMappings,"Last Kanban content stored":this.lastKanbanContent.has(t.path),"Auto-synced files":Array.from(this.autoSyncedFiles),"Current file auto-synced":this.autoSyncedFiles.has(t.path),"isUpdatingFromKanban flag":this.isUpdatingFromKanban,"lastFileContent length":this.lastFileContent.length,"Last file update timestamp":this.lastFileUpdateMap.get(t.path)};if(this.lastKanbanContent.has(t.path)){let e=this.lastKanbanContent.get(t.path);n["Stored content preview"]=`${e.substring(0,200)}...`}this.debugOutput("Kanban Sync Debug Info",n),new c.Notice("Debug info logged to console. Check Developer Tools.")}}),this.addCommand({id:"reset-kanban-autosync-cache",name:"Reset Kanban auto-sync cache",callback:()=>{this.autoSyncedFiles.clear(),new c.Notice("Auto-sync cache cleared. Kanban boards will be auto-synced again when opened."),this.logger.log("Auto-sync cache cleared")}}),this.addCommand({id:"test-checkbox-update",name:"Test checkbox update function",callback:()=>{if(!this.settings.showDebugInfo){new c.Notice("Enable debug mode first to use this command.");return}let t=[{input:`- [/] Main task
  - [ ] Sub-task 1
  - [x] Sub-task 2`,target:"[x]",expected:`- [x] Main task
  - [ ] Sub-task 1
  - [x] Sub-task 2`},{input:"- [ ] Simple task",target:"[/]",expected:"- [/] Simple task"}];console.log("=== Testing Checkbox Update Function ==="),t.forEach((s,o)=>{let a=this.updateCheckboxStateInCardText(s.input,s.target),i=a===s.expected;console.log(`Test ${o+1}: ${i?"PASSED":"FAILED"}`),console.log(`Input: ${s.input}`),console.log(`Expected: ${s.expected}`),console.log(`Got: ${a}`),console.log("---")}),console.log("=== Testing Position Finding Function ===");let n=["## Todo","- [ ] Task 1","  - Sub item","- [/] Task 2","","## In Progress","- [/] Task 3","- [x] Task 4"];[{card:`- [ ] Task 1
  - Sub item`,column:"Todo",expectedPos:1},{card:"- [/] Task 3",column:"In Progress",expectedPos:6},{card:"- [x] Non-existent",column:"Todo",expectedPos:-1}].forEach((s,o)=>{let a=this.findCardPositionInContent(s.card,n,s.column),i=a===s.expectedPos;console.log(`Position Test ${o+1}: ${i?"PASSED":"FAILED"}`),console.log(`Card: ${s.card}`),console.log(`Column: ${s.column}`),console.log(`Expected Position: ${s.expectedPos}`),console.log(`Got Position: ${a}`),console.log("---")}),new c.Notice("Checkbox update and position finding tests completed. Check console for results.")}}),this.addCommand({id:"reset-kanban-conflicts",name:"Reset Kanban conflict state",callback:()=>{this.isUpdatingFromKanban=!1,this.lastKanbanContent.clear(),this.autoSyncedFiles.clear(),this.kanbanNormalizationDetector.clear(),new c.Notice("Kanban conflict state reset. All tracking data cleared."),this.settings.showDebugInfo&&(console.log("Reset all Kanban conflict tracking:"),console.log("- isUpdatingFromKanban: false"),console.log("- lastKanbanContent: cleared"),console.log("- autoSyncedFiles: cleared"),console.log("- kanbanNormalizationDetector: cleared"))}}),this.addCommand({id:"test-kanban-normalization-detection",name:"Test Kanban normalization detection",callback:async()=>{let t=this.app.workspace.getActiveFile();if(!t){new c.Notice("No active file");return}if(!this.isKanbanBoard(t)){new c.Notice("Current file is not a Kanban board");return}if(!this.settings.showDebugInfo){new c.Notice("Enable debug mode first to use this command.");return}let n=await this.app.vault.read(t);console.log("=== Kanban Normalization Detection Test ==="),console.log(`File: ${t.path}`),console.log(`Protection enabled: ${this.settings.enableKanbanNormalizationProtection}`);let e=this.kanbanNormalizationDetector.get(t.path);e?console.log("Detector state:",{lastKanbanUIInteraction:new Date(e.lastKanbanUIInteraction).toISOString(),checkpointsCount:e.preChangeCheckpoints.size,pendingCheck:e.pendingNormalizationCheck!==null}):console.log("No detector state found for this file");let s=n.replace(/\[\/\]/g,"[/]"),o=n.replace(/\[\/\]/g,"[x]"),a=this.analyzeCheckboxNormalizationPatterns(s,o);console.log("Pattern analysis result:",a);let i=this.detectImmediateKanbanNormalization(s,o);if(console.log("Immediate normalization detection:",i),i){let l=this.revertKanbanNormalization(s,o,t);console.log("Revert test:",{originalLength:s.length,normalizedLength:o.length,revertedLength:l.length,revertedMatchesOriginal:l===s})}new c.Notice("Normalization detection test completed. Check console for results.")}}),this.addCommand({id:"test-custom-checkbox-counting",name:"Test custom checkbox state counting",callback:()=>{let t=this.app.workspace.getActiveFile();if(!t){new c.Notice("No active file");return}this.app.vault.read(t).then(n=>{if(console.log("=== Custom Checkbox State Test ==="),console.log(`File: ${t.path}`),console.log(`Custom checkbox states enabled: ${this.settings.enableCustomCheckboxStates}`),this.settings.enableCustomCheckboxStates){let e=this.countTasksByCheckboxState(n);console.log("Task counts by checkbox state:",e);let s=e[" "]||0,o=e.x||0,a=0;for(let[i,l]of Object.entries(e))i!==" "&&i!=="x"&&i.trim()!==""&&(a+=l);console.log(`Incomplete: ${s}, Completed: ${o}, Custom states: ${a}`),console.log(`Total tasks: ${s+o+a}`),console.log(`Progress: ${Math.round(o/(s+o+a)*100)}%`)}else{console.log("Custom checkbox states are disabled");let e=(n.match(/- \[ \]/g)||[]).length,s=(n.match(/- \[x\]/gi)||[]).length;console.log(`Legacy counting - Incomplete: ${e}, Completed: ${s}`)}new c.Notice("Custom checkbox state test completed. Check console for results.")})}}),this.addCommand({id:"test-card-movement-detection",name:"Test card movement detection logic",callback:()=>{let t=this.app.workspace.getActiveFile();if(!t){new c.Notice("No active file");return}if(!this.isKanbanBoard(t)){new c.Notice("Current file is not a Kanban board");return}console.log("=== Card Movement Detection Test ==="),console.log(`File: ${t.path}`),console.log(`Last Kanban content stored: ${this.lastKanbanContent.has(t.path)}`),console.log(`Auto-synced: ${this.autoSyncedFiles.has(t.path)}`),console.log(`isUpdatingFromKanban: ${this.isUpdatingFromKanban}`);let n=this.lastFileUpdateMap.get(t.path);if(n){let s=Date.now()-n;console.log(`Time since last update: ${s}ms`)}else console.log("No last update time recorded");let e=["- [x] [[Test Card]]","- [/] [[Another Card]]","- [ ] [[Todo Card]]","- [-] [[Cancelled Card]]"];console.log("Testing card normalization:"),e.forEach(s=>{let o=this.normalizeCardContentForComparison(s);console.log(`"${s}" -> "${o}"`)}),new c.Notice("Card movement detection test completed. Check console for results.")}}),this.addCommand({id:"simulate-card-movement",name:"Simulate card movement detection",callback:async()=>{let t=this.app.workspace.getActiveFile();if(!t){new c.Notice("No active file");return}if(!this.isKanbanBoard(t)){new c.Notice("Current file is not a Kanban board");return}let n=this.lastKanbanContent.get(t.path);if(!n){new c.Notice("No previous content stored. Try moving a card first.");return}let e=await this.app.vault.read(t);console.log("=== Simulating Card Movement Detection ==="),console.log(`File: ${t.path}`);try{let s=await this.detectActualCardMovements(n,e,t);if(console.log(`Detected ${s.length} actual card movements:`),s.forEach((o,a)=>{console.log(`${a+1}. "${o.card.substring(0,40)}..." from "${o.oldColumn}" to "${o.newColumn}"`)}),s.length>0){console.log("Testing updateCardCheckboxStatesInKanban with detected movements...");let o=await this.updateCardCheckboxStatesInKanban(n,e,t,s);console.log(`Content updated. Original length: ${e.length}, Updated length: ${o.length}`),console.log(o!==e?"Content would be updated with checkbox state changes.":"No checkbox state changes needed.")}}catch(s){console.error("Error in simulation:",s)}new c.Notice("Card movement simulation completed. Check console for results.")}}),this.addCommand({id:"debug-kanban-mappings-and-board-state",name:"Debug Kanban mappings and board state",callback:async()=>{let t=this.app.workspace.getActiveFile();if(!t){new c.Notice("No active file");return}if(!this.isKanbanBoard(t)){new c.Notice("Current file is not a Kanban board");return}console.log("=== Kanban Mappings and Board State Debug ==="),console.log(`File: ${t.path}`),console.log(`enableCustomCheckboxStates: ${this.settings.enableCustomCheckboxStates}`),console.log("Current checkbox mappings:"),this.settings.kanbanColumnCheckboxMappings.forEach((s,o)=>{console.log(`  ${o+1}. "${s.columnName}" \u2192 "${s.checkboxState}"`)});let n=await this.app.vault.read(t),e=await this.parseKanbanBoardContent(n,t);console.log(`
Current board state analysis:`);for(let[s,o]of Object.entries(e)){let a=this.getCheckboxStateForColumn(s);console.log(`
Column: "${s}" (expected state: "${a}")`),console.log(`  Total cards: ${o.items.length}`);let i={},l=0,r=0;o.items.forEach((h,g)=>{let d=h.text.match(/^(\s*- )\[([^\]]*)\]/);if(d){let u=`[${d[2]}]`;i[u]=(i[u]||0)+1,u===a?l++:(r++,console.log(`    ${g}: "${h.text.substring(0,50)}..." has "${u}" but should be "${a}"`))}else console.log(`    ${g}: "${h.text.substring(0,50)}..." - no checkbox found`)}),console.log("  State distribution:",i),console.log(`  Correct states: ${l}, Incorrect states: ${r}`)}new c.Notice("Kanban mappings and board state debug completed. Check console for results.")}}),this.addCommand({id:"manual-trigger-kanban-change",name:"Manually trigger Kanban board change detection",callback:async()=>{let t=this.app.workspace.getActiveFile();if(!t){new c.Notice("No active file");return}if(!this.isKanbanBoard(t)){new c.Notice("Current file is not a Kanban board");return}console.log("=== Manual Kanban Change Trigger ==="),console.log(`File: ${t.path}`);try{let n=await this.app.vault.read(t);console.log(`Current content length: ${n.length}`),await this.handleKanbanBoardChange(t,n),new c.Notice("Manually triggered Kanban board change detection. Check console for logs.")}catch(n){console.error("Error in manual trigger:",n),new c.Notice(`Error: ${n.message}`)}}}),this.addCommand({id:"fix-all-checkbox-states",name:"Fix all checkbox states in current Kanban board",callback:async()=>{let t=this.app.workspace.getActiveFile();if(!t){new c.Notice("No active file");return}if(!this.isKanbanBoard(t)){new c.Notice("Current file is not a Kanban board");return}if(!this.settings.enableCustomCheckboxStates){new c.Notice("Custom checkbox states are disabled. Enable them first in settings.");return}try{this.isUpdatingFromKanban=!0,console.log("=== Fixing All Checkbox States ==="),console.log(`File: ${t.path}`);let n=await this.app.vault.read(t),e=await this.parseKanbanBoardContent(n,t),s=n.split(`
`),o=0;for(let[a,i]of Object.entries(e)){let l=this.getCheckboxStateForColumn(a);console.log(`Fixing column "${a}" to checkbox state "${l}" (${i.items.length} cards)`);for(let r of i.items){let h=r.text.match(/^(\s*- )\[([^\]]*)\]/),g=h?`[${h[2]}]`:null;if(g!==l){let d=this.updateCheckboxStateInCardText(r.text,l);if(d!==r.text){let u=this.findCardPositionInContent(r.text,s,a);if(u!==-1){let p=r.text.split(`
`),m=d.split(`
`);s.splice(u,p.length,...m),o++,console.log(`  Fixed card: "${r.text.substring(0,30)}..." from "${g}" to "${l}"`)}}}}}if(o>0){let a=s.join(`
`);await this.app.vault.modify(t,a),this.lastKanbanContent.set(t.path,a),console.log(`Fixed ${o} checkbox states in ${t.basename}`),new c.Notice(`Fixed ${o} checkbox states in ${t.basename}`)}else console.log("No changes needed - all checkbox states are already correct"),new c.Notice("All checkbox states are already correct")}catch(n){console.error("Error fixing checkbox states:",n),new c.Notice(`Error fixing checkbox states: ${n.message}`)}finally{setTimeout(()=>{this.isUpdatingFromKanban=!1},200)}}})}checkDataviewAPI(){this.dvAPI=K(this.app),this.dvAPI||(this.dataviewCheckInterval=window.setInterval(()=>{this.dvAPI=K(this.app),this.dvAPI&&(this.dataviewCheckInterval&&(clearInterval(this.dataviewCheckInterval),this.dataviewCheckInterval=null),this.sidebarView&&this.lastActiveFile&&this.sidebarView.updateProgressBar(this.lastActiveFile))},2e3))}async updateLastFileContent(t){t&&(this.lastFileContent=await this.app.vault.read(t))}async activateView(){try{let{workspace:t}=this.app,n=t.getLeavesOfType("progress-tracker");if(n.length>0){t.revealLeaf(n[0]);return}t.onLayoutReady(()=>{let e=t.getLeftLeaf(!1);e&&(e.setViewState({type:"progress-tracker",active:!0}),t.revealLeaf(e))})}catch(t){console.error("Error activating view:",t),new c.Notice("Error activating Task Progress Bar view. Please try again later.")}}onunload(){try{this.dataviewCheckInterval&&(clearInterval(this.dataviewCheckInterval),this.dataviewCheckInterval=null),this.sidebarView&&this.sidebarView.clearCompletedFilesCache(),this.lastKanbanContent.clear(),this.autoSyncedFiles.clear(),this.lastFileUpdateMap.clear(),this.kanbanNormalizationDetector.clear(),this.fileOperationLimiter.clear(),this.isUpdatingFromKanban=!1,this.lastActiveFile=null,this.lastFileContent="";let t=document.getElementById("progress-tracker-max-tabs-height");t&&t.remove(),this.logger.log("Plugin cleanup completed successfully")}catch(t){console.error("Error during plugin cleanup:",t)}}async loadSettings(){this.settings=Object.assign({},M,await this.loadData())}async saveSettings(){try{await this.saveData(this.settings),this.applyMaxTabsHeightStyle()}catch(t){console.error("Error saving settings:",t),new c.Notice("Error saving settings. See console for details.")}}applyMaxTabsHeightStyle(){try{let t=document.getElementById("progress-tracker-max-tabs-height");t&&t.remove();let n=this.settings.maxTabsHeight;if(!this.isValidCSSValue(n)){this.logger.error(`Invalid CSS value for maxTabsHeight: ${n}`);return}let e=document.createElement("style");e.id="progress-tracker-max-tabs-height",this.settings.showDebugInfo||(e.textContent=`
					.workspace-tabs.mod-top:has(.progress-tracker-leaf) {
						max-height: ${n} !important;
					}
				`,document.head.appendChild(e)),this.logger.log(`Applied max-tabs-height: ${n} to Progress Tracker view`)}catch(t){this.logger.error("Error applying max tabs height style",t)}}isValidCSSValue(t){return!t||typeof t!="string"?!1:t==="auto"||t==="none"?!0:/^(\d+(\.\d+)?)(px|em|rem|vh|%)$/.test(t.trim())}debugOutput(t,n){this.settings.showDebugInfo&&(this.logger.log(`=== ${t} ===`),Object.entries(n).forEach(([e,s])=>{this.logger.log(`${e}: ${typeof s=="object"?JSON.stringify(s):s}`)}))}isValidFile(t){return t?t.path.includes("..")||t.path.includes("//")?(this.logger.error(`Unsafe file path detected: ${t.path}`),!1):t.path.endsWith(".md")?t.stat.size>10*1024*1024?(this.logger.error(`File too large: ${t.path} (${t.stat.size} bytes)`),!1):!0:(this.logger.warn(`Non-markdown file: ${t.path}`),!1):!1}isValidContent(t){if(typeof t!="string")return!1;if(t.length>5*1024*1024)return this.logger.error(`Content too large: ${t.length} characters`),!1;let n=[/<script[^>]*>/i,/javascript:/i,/data:text\/html/i,/vbscript:/i];for(let e of n)if(e.test(t))return this.logger.error("Suspicious content pattern detected"),!1;return!0}checkRateLimit(t){let n=Date.now(),e=this.fileOperationLimiter.get(t);return e&&n-e<this.FILE_OPERATION_DELAY?(this.logger.warn(`Rate limited file operation: ${t}`),!1):(this.fileOperationLimiter.set(t,n),!0)}handleError(t,n,e=!1){let s=t instanceof Error?t.message:t,o=`${n}: ${s}`;this.logger.error(o,t instanceof Error?t:void 0),e&&new c.Notice(`Progress Tracker Error: ${s}`)}async safeAsyncOperation(t,n,e){try{return await t()}catch(s){return this.handleError(s,n,!1),e}}hasTaskContentChanged(t,n){let e=t.split(`
`),s=n.split(`
`),o=e.filter(i=>i.trim().match(/^[-*] \[[^\]]*\]/i)),a=s.filter(i=>i.trim().match(/^[-*] \[[^\]]*\]/i));if(o.length!==a.length)return this.settings.showDebugInfo&&console.log("Task count changed:",o.length,"->",a.length),!0;for(let i=0;i<o.length;i++)if(o[i]!==a[i])return this.settings.showDebugInfo&&console.log("Task content changed:",o[i],"->",a[i]),!0;return this.settings.showDebugInfo&&console.log("No task-related changes detected"),!1}isKanbanBoard(t){return this.sidebarView?this.sidebarView.isKanbanBoard(t):!1}async handleKanbanBoardChange(t,n){try{if(!this.isValidFile(t)){this.logger.error(`Invalid file for Kanban board change: ${t==null?void 0:t.path}`);return}if(!this.isValidContent(n)){this.logger.error("Invalid content for Kanban board change");return}if(!this.checkRateLimit(t.path))return;if(this.logger.log(`handleKanbanBoardChange called for: ${t.path}`),this.logger.log(`Settings - enableKanbanToFileSync: ${this.settings.enableKanbanToFileSync}, enableCustomCheckboxStates: ${this.settings.enableCustomCheckboxStates}`),!this.settings.enableKanbanToFileSync||!this.settings.enableCustomCheckboxStates){this.settings.showDebugInfo&&console.log("Kanban sync disabled, skipping...");return}let e=t.path,s=this.lastKanbanContent.get(e)||"";if(this.isUpdatingFromKanban){this.settings.showDebugInfo&&console.log("Skipping card movement detection - currently updating from auto-sync or other operations");return}if(this.autoSyncedFiles.has(e)){let i=Date.now()-(this.lastFileUpdateMap.get(e)||0);if(i<2e3){this.settings.showDebugInfo&&console.log(`Skipping card movement detection - auto-sync ran recently (${i}ms ago)`),this.lastKanbanContent.set(e,n);return}}if(this.lastFileUpdateMap.set(e,Date.now()),this.settings.showDebugInfo&&console.log(`Old content length: ${s.length}, New content length: ${n.length}`),this.lastKanbanContent.set(e,n),!s){this.settings.showDebugInfo&&console.log(`First time seeing Kanban board: ${e}, storing content for next time`);return}if(s===n){this.settings.showDebugInfo&&console.log("Content unchanged, skipping...");return}let o=await this.detectActualCardMovements(s,n,t);this.settings.showDebugInfo&&console.log(`Detected ${o.length} actual card movements:`,o);let a=n;o.length>0&&(a=await this.updateCardCheckboxStatesInKanban(s,n,t,o),this.settings.showDebugInfo&&console.log(`Updated content for ${o.length} card movements`)),this.settings.enableKanbanNormalizationProtection&&await this.detectKanbanNormalization(t,s,a,o)&&(this.settings.showDebugInfo&&console.log("Detected Kanban plugin normalization - protecting custom checkbox states for non-moved cards"),a=await this.protectCustomCheckboxStatesSelective(t,s,a,o)),a=await this.syncAllCheckboxStatesToMappings(t,a),a!==n?(this.settings.showDebugInfo&&console.log(`Content will be updated. Original length: ${n.length}, Final length: ${a.length}`),this.isUpdatingFromKanban=!0,await this.app.vault.modify(t,a),this.lastKanbanContent.set(e,a),await this.forceRefreshKanbanUI(t),this.settings.showDebugInfo&&console.log(`Successfully updated checkbox states in Kanban board: ${t.basename}`),setTimeout(()=>{this.isUpdatingFromKanban=!1},300)):this.settings.showDebugInfo&&console.log("No checkbox state changes needed")}catch(e){this.handleError(e,"handleKanbanBoardChange",!1)}}async detectActualCardMovements(t,n,e){try{let s=[],o=await this.parseKanbanBoardContent(t,e),a=await this.parseKanbanBoardContent(n,e),i=new Map,l=new Map;for(let[r,h]of Object.entries(o))h.items.forEach((g,d)=>{let u=this.normalizeCardContentForComparison(g.text);i.has(u)||i.set(u,[]),i.get(u).push({column:r,index:d,originalText:g.text})});for(let[r,h]of Object.entries(a))h.items.forEach((g,d)=>{let u=this.normalizeCardContentForComparison(g.text);l.has(u)||l.set(u,[]),l.get(u).push({column:r,index:d,originalText:g.text})});for(let[r,h]of l){let g=i.get(r)||[];for(let d of h)if(!g.some(p=>p.column===d.column)&&g.length>0){let p=new Map,m=new Map;g.forEach(b=>{p.set(b.column,(p.get(b.column)||0)+1)}),h.forEach(b=>{m.set(b.column,(m.get(b.column)||0)+1)});for(let[b,w]of p)if((m.get(b)||0)<w){s.push({card:r,oldColumn:b,newColumn:d.column,cardIndex:d.index}),this.settings.showDebugInfo&&console.log(`Detected card movement: "${r.substring(0,30)}..." from "${b}" to "${d.column}"`);break}}}return s}catch(s){return this.handleError(s,"detectActualCardMovements",!1),[]}}normalizeCardContentForComparison(t){return t.replace(/^(\s*- )\[[^\]]*\](.*)$/gm,"$1$2").trim()}async updateCardCheckboxStatesInKanban(t,n,e,s){try{if(this.settings.showDebugInfo&&(console.log("Starting card checkbox state update process..."),console.log(`Processing ${s.length} actual card movements`)),s.length===0)return this.settings.showDebugInfo&&console.log("No actual card movements to process, returning original content"),n;let o=await this.parseKanbanBoardContent(n,e),a=n.split(`
`),i=0;for(let l of s){let{card:r,oldColumn:h,newColumn:g,cardIndex:d}=l;this.settings.showDebugInfo&&console.log(`Processing movement: "${r.substring(0,30)}..." from "${h}" to "${g}"`);let u=o[g];if(!u){this.settings.showDebugInfo&&console.log(`Target column "${g}" not found in new content`);continue}if(d>=u.items.length){this.settings.showDebugInfo&&console.log(`Card index ${d} out of range for column "${g}" (has ${u.items.length} items)`);continue}let p=u.items[d],m=this.normalizeCardContentForComparison(p.text);if(m!==r){this.settings.showDebugInfo&&console.log(`Card at index ${d} doesn't match expected content. Expected: "${r}", Found: "${m}"`);continue}let b=this.getCheckboxStateForColumn(g),w=this.updateCheckboxStateInCardText(p.text,b);if(this.settings.showDebugInfo&&(console.log(`Target checkbox state: "${b}"`),console.log(`Original card: ${p.text}`),console.log(`Updated card: ${w}`)),w!==p.text){let k=this.findCardPositionByIndex(a,g,d);if(k!==-1){let y=p.text.split(`
`),v=w.split(`
`);a.splice(k,y.length,...v),i++,this.settings.showDebugInfo&&console.log(`Successfully updated card checkbox state at position ${k} (index ${d}) from "${h}" to "${g}": ${b}`)}else this.settings.showDebugInfo&&console.log(`Could not find position for card at index ${d} in column "${g}"`)}else this.settings.showDebugInfo&&console.log("No changes needed for card (already has correct checkbox state)")}return this.settings.showDebugInfo&&console.log(`Card checkbox update complete. Changes found: ${i} out of ${s.length} movements`),a.join(`
`)}catch(o){return this.handleError(o,"updateCardCheckboxStatesInKanban",!1),n}}async parseKanbanBoardContent(t,n){let e={};try{this.settings.showDebugInfo&&console.log(`Parsing Kanban content for: ${n.path}`);let s=t.split(`
`),o="";for(let a=0;a<s.length;a++){let i=s[a];if(i.startsWith("## ")){o=i.substring(3).trim(),e[o]||(e[o]={items:[]}),this.settings.showDebugInfo&&console.log(`Found column: ${o}`);continue}if(o&&i.trim().startsWith("- ")){let l=i,r=a+1;for(;r<s.length&&(s[r].startsWith("  ")||s[r].startsWith("	")||s[r].trim()==="");)l+=`
`+s[r],r++;e[o].items.push({text:l}),this.settings.showDebugInfo&&console.log(`Found card in ${o}: ${l.substring(0,50)}...`),a=r-1}}this.settings.showDebugInfo&&(console.log(`Parsed ${Object.keys(e).length} columns:`,Object.keys(e)),Object.entries(e).forEach(([a,i])=>{console.log(`  ${a}: ${i.items.length} items`),i.items.forEach((l,r)=>{console.log(`    ${r}: ${l.text.substring(0,50)}...`)})}))}catch(s){console.error("Error parsing Kanban content:",s),this.settings.showDebugInfo&&console.error("Error details:",s)}return e}findCardInKanban(t,n){let e=t.trim();for(let[s,o]of Object.entries(n))for(let a of o.items)if(a.text.trim()===e)return this.settings.showDebugInfo&&console.log(`Found exact match for card "${e.substring(0,30)}..." in column "${s}"`),s;for(let[s,o]of Object.entries(n))for(let a of o.items){let i=a.text.trim();if(this.areCardsEquivalent(e,i))return this.settings.showDebugInfo&&console.log(`Found fuzzy match for card "${e.substring(0,30)}..." in column "${s}"`),s}return this.settings.showDebugInfo&&console.log(`No match found for card "${e.substring(0,30)}..." in any column`),null}areCardsEquivalent(t,n){let e=this.extractMainLinkFromCard(t),s=this.extractMainLinkFromCard(n);return!!(e&&s&&e===s)}extractMainLinkFromCard(t){let n=t.match(/\[\[([^\]]+)\]\]/);if(n)return n[1];let e=t.match(/\[([^\]]+)\]\(([^)]+)\)/);return e?e[2]:null}extractObsidianLinks(t){let n=[],e=/\[\[(.*?)\]\]/g,s;for(;(s=e.exec(t))!==null;){let[o,a]=s,[i,l]=a.split("|").map(r=>r.trim());n.push({path:i,alias:l})}return n}extractMarkdownLinks(t){let n=[],e=/\[(.*?)\]\((.*?)\)/g,s;for(;(s=e.exec(t))!==null;){let[o,a,i]=s;n.push({text:a.trim(),url:i.trim()})}return n}getCheckboxStateForColumn(t){if(!this.settings.enableCustomCheckboxStates)return"[ ]";let n=this.settings.kanbanColumnCheckboxMappings.find(e=>e.columnName.toLowerCase()===t.toLowerCase());return n?n.checkboxState:"[ ]"}findCardPositionInContent(t,n,e){let s="",o=!1;for(let a=0;a<n.length;a++){let i=n[a];if(i.startsWith("## ")){s=i.substring(3).trim(),o=s.toLowerCase()===e.toLowerCase();continue}if(o&&i.trim().startsWith("- ")){let l=i,r=a+1;for(;r<n.length&&(n[r].startsWith("  ")||n[r].startsWith("	")||n[r].trim()==="");)l+=`
`+n[r],r++;if(l.trim()===t.trim())return this.settings.showDebugInfo&&console.log(`Found card at position ${a} in column "${e}"`),a;a=r-1}}return this.settings.showDebugInfo&&console.log(`Card not found in column "${e}"`),-1}findCardPositionByIndex(t,n,e){let s="",o=!1,a=0;for(let i=0;i<t.length;i++){let l=t[i];if(l.startsWith("## ")){s=l.substring(3).trim(),o=s.toLowerCase()===n.toLowerCase(),a=0;continue}if(o&&l.trim().startsWith("- ")){if(a===e)return this.settings.showDebugInfo&&console.log(`Found card at position ${i} (index ${e}) in column "${n}"`),i;a++;let r=i+1;for(;r<t.length&&(t[r].startsWith("  ")||t[r].startsWith("	")||t[r].trim()==="");)r++;i=r-1}}return this.settings.showDebugInfo&&console.log(`Card at index ${e} not found in column "${n}" (found ${a} cards total)`),-1}updateCheckboxStateInCardText(t,n){let e=t.split(`
`);if(e.length===0)return t;let s=/^(\s*[-*] )\[[^\]]*\](.*)$/;return s.test(e[0])&&(e[0]=e[0].replace(s,(o,a,i)=>`${a}${n}${i}`)),e.join(`
`)}countTasksByCheckboxState(t){let n={},e=t.split(`
`);for(let s of e){let o=s.trim().match(/^- \[([^\]]*)\]/);if(o){let a=o[1];n[a]=(n[a]||0)+1}}return n}async detectKanbanNormalization(t,n,e,s=[]){try{let o=t.path,a=Date.now();this.kanbanNormalizationDetector.has(o)||this.kanbanNormalizationDetector.set(o,{preChangeCheckpoints:new Map,lastKanbanUIInteraction:0,pendingNormalizationCheck:null});let i=this.kanbanNormalizationDetector.get(o),l=this.analyzeCheckboxNormalizationPatterns(n,e),r=this.detectUnwantedKanbanNormalization(n,e,s);return i.lastKanbanUIInteraction=a,this.settings.showDebugInfo&&console.log(`Kanban normalization analysis for ${o}:`,{normalizationPatterns:l,hasUnwantedNormalization:r,contentLengthSame:n.length===e.length}),r||l.hasNormalization}catch(o){return console.error("Error detecting Kanban normalization:",o),!1}}detectUnwantedKanbanNormalization(t,n,e=[]){let s=t.split(`
`),o=n.split(`
`);for(let a=0;a<Math.min(s.length,o.length);a++){let i=s[a],l=o[a],r="";for(let d=a;d>=0;d--)if(s[d].startsWith("## ")){r=s[d].substring(3).trim();break}let h=i.match(/^(\s*- )\[([^\]]*)\]/),g=l.match(/^(\s*- )\[([^\]]*)\]/);if(h&&g&&r){let d=h[2],u=g[2],p=this.getCheckboxStateForColumn(r),m=p.replace(/[\[\]]/g,""),b=e.some(w=>w.newColumn.toLowerCase()===r.toLowerCase());if(d===m&&u==="x"&&m!=="x"&&!b)return this.settings.showDebugInfo&&console.log(`Detected unwanted normalization in column "${r}": [${d}] \u2192 [${u}] (expected: ${p})`),!0}}return!1}analyzeCheckboxNormalizationPatterns(t,n){let e=t.split(`
`),s=n.split(`
`),o=[];for(let a=0;a<Math.min(e.length,s.length);a++){let i=e[a],l=s[a],r=i.match(/^(\s*- )\[([^\]]*)\]/),h=l.match(/^(\s*- )\[([^\]]*)\]/);if(r&&h){let g=r[2],d=h[2];g!==d&&g!==" "&&g!=="x"&&(d==="x"||d===" ")&&o.push({line:a,from:`[${g}]`,to:`[${d}]`})}}return{hasNormalization:o.length>0,normalizedStates:o}}async protectCustomCheckboxStates(t,n,e){try{let s=t.path,o=this.analyzeCheckboxNormalizationPatterns(n,e);if(!o.hasNormalization){this.settings.showDebugInfo&&console.log("No normalization detected, no protection needed");return}let a=this.restoreCustomCheckboxStates(n,e,o.normalizedStates,t);a!==e&&(this.settings.showDebugInfo&&(console.log(`Protecting ${o.normalizedStates.length} custom checkbox states from normalization`),o.normalizedStates.forEach(i=>{console.log(`  Line ${i.line}: ${i.from} \u2192 ${i.to} (restoring ${i.from})`)})),this.isUpdatingFromKanban=!0,await this.app.vault.modify(t,a),this.lastKanbanContent.set(s,a),setTimeout(()=>{this.isUpdatingFromKanban=!1},300),this.settings.showDebugInfo&&console.log(`Successfully protected custom checkbox states in: ${t.basename}`))}catch(s){console.error("Error protecting custom checkbox states:",s),this.settings.showDebugInfo&&console.error("Protection error details:",s)}}async protectCustomCheckboxStatesSelective(t,n,e,s){try{let o=this.analyzeCheckboxNormalizationPatterns(n,e);if(!o.hasNormalization)return this.settings.showDebugInfo&&console.log("No normalization detected, no selective protection needed"),e;let a=this.filterLegitimateNormalizations(o.normalizedStates,s,e,t),i=o.normalizedStates.filter(r=>!a.includes(r));if(i.length===0)return this.settings.showDebugInfo&&console.log("All normalizations are legitimate movements, no protection needed"),e;let l=this.restoreCustomCheckboxStates(n,e,i,t);return this.settings.showDebugInfo&&(console.log(`Selectively protecting ${i.length} out of ${o.normalizedStates.length} normalized states`),console.log(`Allowing ${a.length} legitimate state changes from card movements`),i.forEach(r=>{console.log(`  Protecting line ${r.line}: ${r.from} \u2192 ${r.to} (restoring ${r.from})`)}),a.forEach(r=>{console.log(`  Allowing line ${r.line}: ${r.from} \u2192 ${r.to} (legitimate movement)`)})),l}catch(o){return console.error("Error in selective protection:",o),this.settings.showDebugInfo&&console.error("Selective protection error details:",o),e}}restoreCustomCheckboxStates(t,n,e,s){let o=n.split(`
`),a=0;for(let i of e){let l=i.line;if(l<o.length){let r=o[l],h=this.findLineColumn(r,o,l);if(h){let g=this.getCheckboxStateForColumn(h);i.from===g&&(o[l]=r.replace(i.to,i.from),a++,this.settings.showDebugInfo&&console.log(`Restored line ${l}: ${i.to} \u2192 ${i.from} for column "${h}"`))}}}return this.settings.showDebugInfo&&console.log(`Restored ${a} out of ${e.length} normalized states`),o.join(`
`)}filterLegitimateNormalizations(t,n,e,s){let o=[],a=e.split(`
`);for(let i of t){let l=i.line;if(l<a.length){let r=a[l],h=this.findLineColumn(r,a,l);if(h&&n.some(d=>d.newColumn.toLowerCase()===h.toLowerCase())){let d=this.getCheckboxStateForColumn(h);i.to===d&&(o.push(i),this.settings.showDebugInfo&&console.log(`Legitimate normalization at line ${l}: ${i.from} \u2192 ${i.to} for column "${h}"`))}}}return o}checkMovementsForUnwantedNormalization(t,n,e){for(let s of t){let{newColumn:o}=s,a=this.getCheckboxStateForColumn(o),i=a.replace(/[\[\]]/g,""),l=n.split(`
`),r=!1,h="";for(let g of l){if(g.startsWith("## ")){h=g.substring(3).trim(),r=h===o;continue}if(r&&g.trim().startsWith("- [")){let d=g.match(/^(\s*- )\[([^\]]*)\]/);if(d){let u=d[2];if(u===i&&i!=="x")return this.settings.showDebugInfo&&console.log(`Movement to column "${o}" would cause unwanted normalization: [${u}] \u2192 [x] (expected: ${a})`),!0}}}}return!1}findLineColumn(t,n,e){for(let s=e;s>=0;s--){let o=n[s];if(o.startsWith("## "))return o.substring(3).trim()}return null}detectImmediateKanbanNormalization(t,n){let e=t.split(`
`),s=n.split(`
`),o=0,a=0;for(let r=0;r<Math.min(e.length,s.length);r++){let h=e[r],g=s[r],d=h.match(/^(\s*- )\[([^\]]*)\]/),u=g.match(/^(\s*- )\[([^\]]*)\]/);if(d&&u){let p=d[2],m=u[2];p!==m&&(o++,p!==" "&&p!=="x"&&m==="x"&&(a++,this.settings.showDebugInfo&&console.log(`Immediate normalization detected: [${p}] \u2192 [${m}] on line ${r}`)))}}let i=a>=2,l=o>0&&a/o>=.5;return this.settings.showDebugInfo&&(console.log(`Enhanced normalization detection: ${a} custom\u2192[x] out of ${o} total changes`),console.log(`hasMultipleCustomToX: ${i}, hasHighCustomToXRatio: ${l}`)),i||l}revertKanbanNormalization(t,n,e){let s=t.split(`
`),o=n.split(`
`),a=[...o],i=0,l=0;for(let r=0;r<Math.min(s.length,o.length);r++){let h=s[r],g=o[r],d=h.match(/^(\s*- )\[([^\]]*)\]/),u=g.match(/^(\s*- )\[([^\]]*)\]/);if(d&&u){let p=d[2],m=u[2];if(p!==m){l++;let b="";for(let w=r;w>=0;w--)if(o[w].startsWith("## ")){b=o[w].substring(3).trim();break}if(b){let k=this.getCheckboxStateForColumn(b).replace(/[\[\]]/g,"");if(p===k&&m!==k){let y=u[1],v=g.substring(u[0].length);a[r]=`${y}[${p}]${v}`,i++,this.settings.showDebugInfo&&console.log(`Reverted line ${r} in column "${b}": [${m}] \u2192 [${p}] (preserving content)`)}else this.settings.showDebugInfo&&console.log(`Line ${r} in column "${b}": [${p}] \u2192 [${m}] (not reverting - expected: [${k}])`)}}}}return this.settings.showDebugInfo&&console.log(`Reverted ${i} out of ${l} normalizations`),a.join(`
`)}async syncAllCheckboxStatesToMappings(t,n){try{this.settings.showDebugInfo&&console.log(`Syncing all checkbox states to mappings for: ${t.path}`);let e=await this.parseKanbanBoardContent(n,t);if(!e||Object.keys(e).length===0)return this.settings.showDebugInfo&&console.log("Could not parse Kanban board structure, returning original content"),n;let s=n.split(`
`),o=0;for(let[a,i]of Object.entries(e)){let l=this.getCheckboxStateForColumn(a);this.settings.showDebugInfo&&console.log(`Syncing column "${a}" to checkbox state "${l}" (${i.items.length} cards)`);for(let r of i.items){let h=r.text.match(/^(\s*- )\[([^\]]*)\]/),g=h?`[${h[2]}]`:null;if(g!==l){let d=this.updateCheckboxStateInCardText(r.text,l);if(d!==r.text){let u=this.findCardPositionInContent(r.text,s,a);if(u!==-1){let p=r.text.split(`
`),m=d.split(`
`);s.splice(u,p.length,...m),o++,this.settings.showDebugInfo&&console.log(`  Synced card at position ${u}: ${r.text.substring(0,30)}... \u2192 ${l} (was ${g})`)}}}else this.settings.showDebugInfo&&console.log(`  Skipping card (already has correct state ${l}): ${r.text.substring(0,30)}...`)}}return this.settings.showDebugInfo&&console.log(`Sync complete: ${o} cards updated to match column mappings`),s.join(`
`)}catch(e){return console.error("Error syncing checkbox states to mappings:",e),this.settings.showDebugInfo&&console.error("Error details:",e),n}}async autoSyncKanbanCheckboxStates(t){try{if(this.isUpdatingFromKanban){this.settings.showDebugInfo&&console.log(`Auto-sync skipped - update already in progress for: ${t.path}`);return}this.settings.showDebugInfo&&console.log(`Starting auto-sync for Kanban board: ${t.path}`),this.isUpdatingFromKanban=!0,this.autoSyncedFiles.add(t.path),this.lastFileUpdateMap.set(t.path,Date.now());let n=await this.app.vault.read(t);this.lastKanbanContent.set(t.path,n);let e=await this.app.vault.read(t),s=await this.parseKanbanBoardContent(e,t),o=e.split(`
`),a=0;for(let[i,l]of Object.entries(s)){let r=this.getCheckboxStateForColumn(i);this.settings.showDebugInfo&&console.log(`Auto-syncing column "${i}" to checkbox state "${r}" (${l.items.length} cards)`);for(let h of l.items){let g=h.text.match(/^(\s*- )\[([^\]]*)\]/),d=g?`[${g[2]}]`:null;if(d!==r){let u=this.updateCheckboxStateInCardText(h.text,r);if(u!==h.text){let p=this.findCardPositionInContent(h.text,o,i);if(p!==-1){let m=h.text.split(`
`),b=u.split(`
`);o.splice(p,m.length,...b),a++,this.settings.showDebugInfo&&console.log(`  Updated card at position ${p}: ${h.text.substring(0,30)}... \u2192 ${r} (was ${d})`)}}}else this.settings.showDebugInfo&&console.log(`  Skipping card (already has correct state ${r}): ${h.text.substring(0,30)}...`)}}if(a>0){let i=o.join(`
`);await this.app.vault.modify(t,i),this.lastKanbanContent.set(t.path,i),this.settings.showDebugInfo&&(console.log(`Auto-sync complete: ${a} cards updated in ${t.basename}`),console.log(`Updated lastKanbanContent for ${t.path} to prevent conflicts`)),new c.Notice(`Auto-synced ${a} card checkbox states in ${t.basename}`)}else this.settings.showDebugInfo&&console.log(`Auto-sync complete: No changes needed for ${t.basename}`)}catch(n){console.error("Error in auto-sync Kanban checkbox states:",n),this.settings.showDebugInfo&&console.error("Error details:",n),this.autoSyncedFiles.delete(t.path)}finally{setTimeout(()=>{this.isUpdatingFromKanban=!1,this.settings.showDebugInfo&&console.log(`Auto-sync flag reset for: ${t.path}`)},200)}}async forceRefreshKanbanUI(t){try{this.settings.showDebugInfo&&console.log(`Attempting to force refresh Kanban UI for: ${t.path}`);let n=this.app.workspace.getActiveFile();if(n&&n.path===t.path){let s=this.app.workspace.getActiveViewOfType(c.MarkdownView);s&&setTimeout(()=>{s.requestSave()},100)}setTimeout(()=>{this.app.workspace.trigger("layout-change")},150);let e=this.app.workspace.getLeavesOfType("markdown");for(let s of e){let o=s.view;if(o.file&&o.file.path===t.path){setTimeout(()=>{o.load()},200);break}}this.settings.showDebugInfo&&console.log(`Force refresh attempts completed for: ${t.path}`)}catch(n){this.settings.showDebugInfo&&console.error("Error forcing Kanban UI refresh:",n)}}},T=class extends c.ItemView{constructor(t,n){super(t);this.currentFile=null;this.isVisible=!1;this.lastUpdateTime=0;this.lastFileUpdateMap=new Map;this.initialLoadComplete=!1;this.completedFilesMap=new Map;this.plugin=n}getViewType(){return"progress-tracker"}getDisplayText(){return"Task progress bar"}getIcon(){return"bar-chart-horizontal"}async onOpen(){this.isVisible=!0,this.initialLoadComplete=!1;let t=this.leaf;t&&t.containerEl&&t.containerEl.addClass("progress-tracker-leaf");let n=this.containerEl.children[1];n.empty(),n.createDiv({cls:"task-progress-container"}).createEl("p",{text:"Loading task progress...",cls:"loading-indicator"})}async updateProgressBar(t,n,e=!1){if(!t)return;this.lastFileUpdateMap.set(t.path,Date.now());let s=Date.now();if(!e&&s-this.lastUpdateTime<100)return;this.lastUpdateTime=s,this.currentFile=t;let a=this.containerEl.children[1].querySelector(".task-progress-container");if(a){(e||!this.initialLoadComplete)&&(a.empty(),this.initialLoadComplete=!0),this.plugin.settings.showUpdateAnimation&&a.classList.add("updating");try{if(n)this.hasTasksInContentExtended(n)?this.createProgressBarFromString(a,n,t):a.querySelector(".no-tasks-message")||(a.empty(),a.createEl("p",{text:"No tasks found in this file",cls:"no-tasks-message"}));else{let i=await this.plugin.app.vault.read(t);this.hasTasksInContentExtended(i)?this.createProgressBarFromString(a,i,t):(a.querySelector(".no-tasks-message")||(a.empty(),a.createEl("p",{text:"No tasks found in this file",cls:"no-tasks-message"})),this.plugin.settings.showDebugInfo&&console.log("No tasks found in file:",t.path))}}catch(i){console.error("Error updating progress bar:",i),a.empty(),a.createEl("p",{text:`Error updating progress bar: ${i.message}`})}finally{this.plugin.settings.showUpdateAnimation&&setTimeout(()=>{a.classList.remove("updating")},this.plugin.settings.updateAnimationDelay)}}}hasTasksInContentExtended(t){return/- \[[^\]]*\]/i.test(t)}async createProgressBarFromString(t,n,e){try{if(this.plugin.settings.showDebugInfo&&(console.log(`Creating progress bar for file: ${e.path}`),console.log(`Content length: ${n.length}`)),!this.plugin.dvAPI){t.querySelector(".dataview-warning-compact")||(t.empty(),t.createDiv({cls:"dataview-warning-compact"}).createEl("span",{text:"Dataview not available",cls:"dataview-warning-text"}));return}let o=0,a=0,i=0;if(this.plugin.settings.enableCustomCheckboxStates){let u=this.countTasksByCheckboxState(n);o=u[" "]||0,a=u.x||0;for(let[p,m]of Object.entries(u))p!==" "&&p!=="x"&&p.trim()!==""&&(i+=m);this.plugin.settings.showDebugInfo&&(console.log("Custom checkbox state counts:",u),console.log(`Incomplete: ${o}, Completed: ${a}, Custom states: ${i}`))}else o=(n.match(/- \[ \]/g)||[]).length,a=(n.match(/- \[x\]/gi)||[]).length;let l=o+a+i,r=0,h=0;if(l===0&&(r=(n.match(/[-*] \[ \]/g)||[]).length,h=(n.match(/[-*] \[x\]/gi)||[]).length,l=r+h),this.plugin.settings.showDebugInfo&&(console.log(`Task counts - incomplete: ${o}, completed: ${a}, custom states: ${i}, total: ${l}`),(r>0||h>0)&&console.log(`Using relaxed regex - found tasks: ${r+h}`)),l===0){t.querySelector(".no-tasks-message")||(t.empty(),t.createEl("p",{text:"No tasks found in this file",cls:"no-tasks-message"}));return}let g=o>0||a>0||i>0?a:h,d=Math.round(g/l*100);this.updateProgressBarUI(t,d,g,l),this.processStatusAndKanbanUpdates(e,d,g,l)}catch(s){console.error("Error creating progress bar from string:",s),t.empty(),t.createEl("p",{text:`Error creating progress bar: ${s.message}`})}}updateProgressBarUI(t,n,e,s){var h;let o=t.querySelector(".progress-layout"),a=t.querySelector(".progress-stats-compact");(!o||!a)&&(t.empty(),o=t.createDiv({cls:"progress-layout"}),o.createEl("div",{cls:"progress-percentage-small"}),o.createDiv({cls:"pt-progress-bar-container"}).createDiv({cls:"progress-element"}).createDiv({cls:"progress-value"}),a=t.createDiv({cls:"progress-stats-compact"}));let i=o.querySelector(".progress-percentage-small");i&&i.setText(`${n}%`);let l=t.querySelector(".progress-value");l&&(l.hasAttribute("data-has-transition")||(l.style.transition="width 0.3s ease-in-out, background-color 0.3s ease",l.setAttribute("data-has-transition","true")),l.style.width=`${n}%`,this.applyProgressColor(l,n));let r=t.querySelector(".progress-element");if(r&&r.setAttribute("data-percentage",n.toString()),a&&(a.empty(),a.createSpan({text:`${e}/${s} tasks`})),this.plugin.settings.showDebugInfo){let g=t.querySelector(".debug-info");g?g.empty():g=t.createDiv({cls:"debug-info"}),g.createEl("p",{text:"Debug info:"}),g.createEl("p",{text:`File: ${(h=this.currentFile)==null?void 0:h.path}`}),g.createEl("p",{text:`Incomplete tasks: ${s-e}`}),g.createEl("p",{text:`Completed tasks: ${e}`}),g.createEl("p",{text:`Total tasks: ${s}`}),g.createEl("p",{text:`Percentage: ${n}%`}),g.createEl("p",{text:`Update time: ${new Date().toISOString()}`}),g.createEl("p",{text:`Color scheme: ${this.plugin.settings.progressColorScheme}`})}else{let g=t.querySelector(".debug-info");g&&g.remove()}}async processStatusAndKanbanUpdates(t,n,e,s){setTimeout(async()=>{try{let o=!1;this.plugin.settings.autoChangeStatus&&(o=await this.updateStatusBasedOnProgress(t,n)),this.plugin.settings.autoUpdateKanban&&(o||!this.completedFilesMap.has(t.path))&&await this.updateKanbanBoards(t,e,s),n===100&&this.plugin.settings.autoUpdateMetadata?this.completedFilesMap.has(t.path)||(await this.updateFileMetadata(t),this.completedFilesMap.set(t.path,!0)):n<100&&this.completedFilesMap.has(t.path)&&this.completedFilesMap.delete(t.path),this.plugin.settings.autoAddToKanban&&this.plugin.settings.autoAddKanbanBoard&&s>0&&!this.completedFilesMap.has(t.path)&&await this.addFileToKanbanBoard(t)}catch(o){console.error("Error in status and Kanban updates:",o),this.plugin.settings.showDebugInfo&&console.error("Error details:",o)}},0)}applyProgressColor(t,n){let e=this.plugin.settings;if(e.progressColorScheme==="default"){t.style.backgroundColor="";return}let s="";n===100?s=e.completeProgressColor:n>=e.mediumProgressThreshold?s=e.highProgressColor:n>=e.lowProgressThreshold?s=e.mediumProgressColor:s=e.lowProgressColor,t.style.backgroundColor!==s&&(t.style.backgroundColor=s),this.plugin.settings.showDebugInfo&&console.log(`Applied color for ${n}%: 
				Color scheme: ${e.progressColorScheme},
				Low threshold: ${e.lowProgressThreshold}%, 
				Medium threshold: ${e.mediumProgressThreshold}%, 
				High threshold: ${e.highProgressThreshold}%,
				Applied color: ${s}`)}clearCompletedFilesCache(){this.completedFilesMap.clear(),this.plugin.settings.showDebugInfo&&console.log("Cleared completed files cache")}async updateStatusBasedOnProgress(t,n){if(!t||!this.plugin.settings.autoChangeStatus)return!1;try{let e=!1,s=this.plugin.settings.statusInProgress;return n===0?s=this.plugin.settings.statusTodo:n===100&&(s=this.plugin.settings.statusCompleted),await this.app.fileManager.processFrontMatter(t,o=>{o.status!==s&&(o.status=s,e=!0),n<100&&this.plugin.settings.autoUpdateFinishedDate&&o.finished&&(delete o.finished,e=!0)}),e&&this.plugin.settings.showDebugInfo&&console.log(`Updated status to "${s}" based on progress ${n}% for file:`,t.path),e}catch(e){return console.error("Error updating status based on progress:",e),!1}}async updateFileMetadata(t){try{await this.app.fileManager.processFrontMatter(t,n=>{let e=!1,s=new Date().toISOString().split("T")[0];if(this.plugin.settings.autoChangeStatus){let o=this.plugin.settings.statusCompleted;n.status!==o&&(n.status=o,e=!0,this.plugin.settings.showDebugInfo&&console.log(`Updating status to ${o} in file:`,t.path))}return this.plugin.settings.autoUpdateFinishedDate&&n.finished!==s&&(n.finished=s,e=!0,this.plugin.settings.showDebugInfo&&console.log(`Updating finished date to ${s} in file:`,t.path)),e})}catch(n){console.error("Error updating file metadata:",n),this.plugin.settings.showDebugInfo&&new c.Notice(`Error updating metadata for ${t.basename}: ${n.message}`)}}async updateKanbanBoards(t,n,e){try{if(!this.plugin.settings.autoUpdateKanban||e===0)return;let s=this.calculateStatusFromProgress(n,e);await this.waitForCacheUpdate(t);let o=await this.getStatusFromYaml(t);o&&(s=o,this.plugin.settings.showDebugInfo&&console.log(`Using status from YAML: ${s} instead of calculated status`)),this.plugin.settings.showDebugInfo&&(console.log(`Searching for Kanban boards that might contain ${t.path}...`),console.log(`Current status is: ${s} (${n}/${e} tasks)`));let a=await this.processKanbanBoards(t,s);a>0&&new c.Notice(`Updated ${a} Kanban board${a>1?"s":""} to move ${t.basename} to ${s} column`)}catch(s){console.error("Error updating Kanban boards:",s),this.plugin.settings.showDebugInfo&&(console.error("Error details:",s),new c.Notice(`Error updating Kanban boards: ${s.message}`))}}calculateStatusFromProgress(t,n){return n===0?this.plugin.settings.statusTodo:t===0?this.plugin.settings.statusTodo:t===n?this.plugin.settings.statusCompleted:this.plugin.settings.statusInProgress}async processKanbanBoards(t,n){let e=t.path.toLowerCase(),s=this.plugin.app.vault.configDir.toLowerCase();if(e.includes(`${s}/plugins/progress-tracker`)||e.includes("kanban"))return this.plugin.settings.showDebugInfo&&console.log(`Skipping plugin or kanban file for kanban processing: ${t.path}`),0;let o=0;if(this.plugin.settings.autoAddKanbanBoard){let i=this.plugin.app.vault.getAbstractFileByPath(this.plugin.settings.autoAddKanbanBoard);if(i instanceof c.TFile){if(i.path===t.path)return this.plugin.settings.showDebugInfo&&console.log(`Skipping target board as it's the file being updated: ${t.path}`),0;let l=await this.plugin.app.vault.read(i);return!this.isKanbanBoard(i)||!this.containsFileReference(l,t)?(this.plugin.settings.showDebugInfo&&console.log(`Target board ${i.path} is not a valid Kanban board or doesn't reference ${t.path}`),0):(this.plugin.settings.showDebugInfo&&console.log(`Processing target Kanban board: ${i.path}`),await this.moveCardInKanbanBoard(i,l,t,n)!==l&&o++,o)}else return this.plugin.settings.showDebugInfo&&console.log(`Target board not found: ${this.plugin.settings.autoAddKanbanBoard}`),0}this.plugin.settings.showDebugInfo&&console.log("No target board set, searching all potential Kanban boards...");let a=this.plugin.app.vault.getMarkdownFiles();for(let i of a){if(i.path===t.path)continue;let l=await this.plugin.app.vault.read(i);if(!this.isKanbanBoard(i)||!this.containsFileReference(l,t))continue;this.plugin.settings.showDebugInfo&&console.log(`Found Kanban board "${i.path}" that references "${t.path}"`),await this.moveCardInKanbanBoard(i,l,t,n)!==l&&o++}return o}escapeRegExp(t){return t?t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"):""}containsFileReference(t,n){let e=n.path,s=e.replace(/\.md$/,""),o=n.basename,a=this.extractObsidianLinks(t);for(let r of a){let{path:h,alias:g}=r;if(h===o||h===s||h===e)return this.plugin.settings.showDebugInfo&&console.log(`Found exact Obsidian link match for ${o}: ${h}`),!0}let i=this.extractMarkdownLinks(t);for(let r of i){let{text:h,url:g}=r;if(g===e||g===s)return this.plugin.settings.showDebugInfo&&console.log(`Found exact Markdown link match for ${o}: ${g}`),!0}return new RegExp(`(?:^|\\s|\\()${this.escapeRegExp(e)}(?:$|\\s|\\))`,"i").test(t)?(this.plugin.settings.showDebugInfo&&console.log(`Found exact filepath match for ${e}`),!0):!1}extractObsidianLinks(t){let n=[],e=/\[\[(.*?)\]\]/g,s;for(;(s=e.exec(t))!==null;){let[o,a]=s,[i,l]=a.split("|").map(r=>r.trim());n.push({path:i,alias:l})}return this.plugin.settings.showDebugInfo&&console.log("Extracted Obsidian links:",n),n}extractMarkdownLinks(t){let n=[],e=/\[(.*?)\]\((.*?)\)/g,s;for(;(s=e.exec(t))!==null;){let[o,a,i]=s;n.push({text:a.trim(),url:i.trim()})}return this.plugin.settings.showDebugInfo&&console.log("Extracted Markdown links:",n),n}async moveCardInKanbanBoard(t,n,e,s){try{await this.waitForCacheUpdate(e);let o=await this.parseKanbanBoard(t);if(!o||Object.keys(o).length===0)return this.plugin.settings.showDebugInfo&&console.log(`Could not parse Kanban board structure in ${t.path}`),n;let a;if(this.plugin.settings.kanbanSyncWithStatus?(a=Object.keys(o).find(u=>u.toLowerCase()===s.toLowerCase()),a||(a=this.findClosestColumnName(Object.keys(o),s))):s===this.plugin.settings.statusCompleted&&(a=Object.keys(o).find(u=>u.toLowerCase()===this.plugin.settings.kanbanCompletedColumn.toLowerCase())),!a)return this.plugin.settings.showDebugInfo&&(console.log(`Could not find column for status "${s}" in Kanban board ${t.path}`),console.log(`Available columns: ${Object.keys(o).join(", ")}`)),n;this.plugin.settings.showDebugInfo&&console.log(`Target column for status "${s}" is "${a}"`);let i=!1,l=n,r=l.split(`
`),h="",g=-1,d=-1;for(let u=0;u<r.length;u++){let p=r[u];if(p.startsWith("## ")){h=p.substring(3).trim();continue}if(h.toLowerCase()!==a.toLowerCase()&&p.trim().startsWith("- ")){let m=this.getCompleteCardContent(r,u);if(this.isExactCardForFile(m.content,e)){g=u,d=u+m.lineCount-1;break}u+=m.lineCount-1}}if(g!==-1&&d!==-1){let u=r.slice(g,d+1);if(this.plugin.settings.enableCustomCheckboxStates&&a){let m=a;u=u.map(b=>this.updateCheckboxStatesInCard(b,m))}r.splice(g,d-g+1);let p=-1;for(let m=0;m<r.length;m++)if(r[m].startsWith(`## ${a}`)){p=m+1;break}if(p!==-1&&(r.splice(p,0,...u),i=!0,this.plugin.settings.showDebugInfo&&(console.log(`Moved card for ${e.path} to column "${a}" in ${t.path}`),this.plugin.settings.enableCustomCheckboxStates))){let m=this.getCheckboxStateForColumn(a);console.log(`Applied checkbox state "${m}" to card`)}}return i&&(l=r.join(`
`),await this.plugin.app.vault.modify(t,l)),l}catch(o){return console.error("Error moving card in Kanban board:",o),this.plugin.settings.showDebugInfo&&console.error("Error details:",o),n}}getCompleteCardContent(t,n){let e=t[n],s=1;for(let o=n+1;o<t.length;o++){let a=t[o];if(a.trim()===""||a.startsWith("  ")||a.startsWith("	"))e+=`
`+a,s++;else break}return{content:e,lineCount:s}}isExactCardForFile(t,n){let e=this.extractObsidianLinks(t),s=this.extractMarkdownLinks(t),o=n.basename,a=n.path,i=a.replace(/\.md$/,"");for(let l of e){let{path:r}=l;if(r===o||r===i||r===a)return this.plugin.settings.showDebugInfo&&console.log(`Found exact Obsidian link match in card: ${r} for file: ${o}`),!0}for(let l of s){let{url:r}=l;if(r===a||r===i)return this.plugin.settings.showDebugInfo&&console.log(`Found exact Markdown link match in card: ${r} for file: ${o}`),!0}return!1}findClosestColumnName(t,n){let e=n.toLowerCase(),s={todo:["to do","todo","backlog","new","not started","pending","open","to-do"],"in progress":["progress","doing","working","ongoing","started","in work","active","current","wip"],completed:["done","complete","finished","closed","resolved","ready","completed"]},o=t.find(a=>a.toLowerCase()===e);if(o)return o;if(e===this.plugin.settings.statusTodo.toLowerCase())for(let a of t){let i=a.toLowerCase();if(s.todo.some(l=>i.includes(l)||l===i))return a}else if(e===this.plugin.settings.statusInProgress.toLowerCase())for(let a of t){let i=a.toLowerCase();if(s["in progress"].some(l=>i.includes(l)||l===i))return a}else if(e===this.plugin.settings.statusCompleted.toLowerCase())for(let a of t){let i=a.toLowerCase();if(s.completed.some(l=>i.includes(l)||l===i))return a}for(let[a,i]of Object.entries(s))if(i.some(l=>e.includes(l)||l.includes(e)))for(let l of t){let r=l.toLowerCase();if(i.some(h=>r.includes(h)||h.includes(r)))return l}for(let a of t)if(a.toLowerCase()===e)return a;for(let a of t)if(a.toLowerCase().includes(e)||e.includes(a.toLowerCase()))return a;if(e===this.plugin.settings.statusTodo.toLowerCase()&&t.length>0)return t[0]}async getStatusFromYaml(t){let n=this.app.metadataCache.getFileCache(t);if(!(n!=null&&n.frontmatter))return this.plugin.settings.showDebugInfo&&console.log(`No frontmatter found for file: ${t.path}`),null;try{let e=n.frontmatter.status;if(typeof e=="string"&&e.trim())return this.plugin.settings.showDebugInfo&&console.log(`Status found for ${t.path}: ${e}`),e.trim();this.plugin.settings.showDebugInfo&&console.log(`No valid status in frontmatter for file: ${t.path}`)}catch(e){console.error(`Error accessing frontmatter for ${t.path}:`,e),this.plugin.settings.showDebugInfo&&console.error("Error details:",e)}return null}async waitForCacheUpdate(t,n=1e3){return new Promise((e,s)=>{let o=setTimeout(()=>{this.plugin.settings.showDebugInfo&&console.log(`Timeout waiting for cache update for ${t.path}`),e()},n),a=i=>{i.path===t.path&&(this.app.metadataCache.off("changed",a),clearTimeout(o),this.plugin.settings.showDebugInfo&&console.log(`Cache updated for ${t.path}`),e())};this.app.metadataCache.on("changed",a)})}async parseKanbanBoard(t){var e,s;let n={};try{let o=this.app.metadataCache.getFileCache(t);if(!o)return this.plugin.settings.showDebugInfo&&console.log(`No cache found for file: ${t.path}`),n;let a=((e=o.frontmatter)==null?void 0:e["kanban-plugin"])==="basic",i=((s=o.headings)==null?void 0:s.filter(r=>r.level===2))||[];if(i.length<1)return this.plugin.settings.showDebugInfo&&console.log(`No H2 headers found in file: ${t.path}`),n;let l=await this.plugin.app.vault.read(t);for(let r=0;r<i.length;r++){let h=i[r],g=h.heading.trim();n[g]={items:[]};let d=h.position.start.offset,u=r<i.length-1?i[r+1].position.start.offset:l.length,p=l.substring(d+h.heading.length+4,u).trim();a?this.extractKanbanPluginItems(p,n[g].items):this.extractMarkdownItems(p,n[g].items)}this.plugin.settings.showDebugInfo&&(console.log(`Parsed Kanban board ${t.path} with columns:`,Object.keys(n)),Object.entries(n).forEach(([r,h])=>{console.log(`Column "${r}" has ${h.items.length} items`)}))}catch(o){console.error(`Error parsing Kanban board ${t.path}:`,o),this.plugin.settings.showDebugInfo&&console.error("Error details:",o)}return n}extractKanbanPluginItems(t,n){let e=t.split(/^- /m).slice(1);for(let s of e){let o="- "+s.trim();n.push({text:o})}}extractMarkdownItems(t,n){let e=t.split(`
`),s="",o=!1;for(let a of e)a.trim().startsWith("- ")?(o&&n.push({text:s.trim()}),s=a,o=!0):o&&(s+=`
`+a);o&&n.push({text:s.trim()})}isKanbanBoard(t){var n;try{let e=this.app.metadataCache.getFileCache(t);if(!e)return this.plugin.settings.showDebugInfo&&console.log(`No cache found for file: ${t.path}`),!1;if(((n=e.frontmatter)==null?void 0:n["kanban-plugin"])==="basic")return this.plugin.settings.showDebugInfo&&console.log(`Detected Kanban plugin board: ${t.path}`),!0;let s=e.headings||[];if(s.length<2)return this.plugin.settings.showDebugInfo&&console.log(`Insufficient headers in file: ${t.path}`),!1;let o=["todo","to do","to-do","backlog","new","ideas","inbox","in progress","doing","working","current","ongoing","done","complete","completed","finished","blocked","waiting"],a=0,i=this.plugin.settings.kanbanCompletedColumn.toLowerCase();for(let r of s){if(r.level!==2)continue;let h=r.heading.toLowerCase();(o.some(g=>h.includes(g))||h===i)&&a++}let l=a>=2;return this.plugin.settings.showDebugInfo&&console.log(`File ${t.path} is ${l?"":"not "}a Kanban board (columns detected: ${a})`),l}catch(e){return console.error(`Error checking if ${t.path} is a Kanban board:`,e),this.plugin.settings.showDebugInfo&&console.error("Error details:",e),!1}}async addFileToKanbanBoard(t){try{if(!this.plugin.settings.autoAddToKanban||!this.plugin.settings.autoAddKanbanBoard)return!1;let n=t.path.toLowerCase(),e=this.plugin.app.vault.configDir.toLowerCase();if(n.includes(`${e}/plugins/progress-tracker`)||n.includes("kanban")||n===this.plugin.settings.autoAddKanbanBoard)return this.plugin.settings.showDebugInfo&&console.log(`Skipping plugin or kanban file: ${t.path}`),!1;let s=this.plugin.settings.autoAddKanbanBoard,o=this.plugin.app.vault.getAbstractFileByPath(s);if(!o||!(o instanceof c.TFile))return this.plugin.settings.showDebugInfo&&console.log(`Could not find Kanban board at path: ${s}`),!1;if(t.path===o.path)return this.plugin.settings.showDebugInfo&&console.log(`Skipping adding kanban board to itself: ${t.path}`),!1;let a=await this.plugin.app.vault.read(o);if(!this.isKanbanBoard(o))return this.plugin.settings.showDebugInfo&&console.log(`File at path ${s} is not a Kanban board`),!1;if(this.containsFileReference(a,t))return this.plugin.settings.showDebugInfo&&console.log(`File ${t.path} is already in Kanban board ${s}`),!1;let i=this.plugin.settings.autoAddKanbanColumn||"Todo";await this.waitForCacheUpdate(t);let l=await this.parseKanbanBoard(o);if(!l||Object.keys(l).length===0)return this.plugin.settings.showDebugInfo&&console.log(`Could not parse Kanban board structure in ${s}`),!1;let r=Object.keys(l).find(m=>m.toLowerCase()===i.toLowerCase());if(r||(r=this.findClosestColumnName(Object.keys(l),i)),!r)return this.plugin.settings.showDebugInfo&&console.log(`Could not find column "${i}" in Kanban board ${s}`),!1;let h=new RegExp(`## ${this.escapeRegExp(r)}\\s*\\n`),g=a.match(h);if(!g)return this.plugin.settings.showDebugInfo&&console.log(`Could not find column "${r}" in Kanban board content`),!1;let d=g.index+g[0].length,u=`- [[${t.basename}]]
`;this.plugin.settings.enableCustomCheckboxStates&&(u=`- ${this.getCheckboxStateForColumn(r)} [[${t.basename}]]
`);let p=a.substring(0,d)+u+a.substring(d);return await this.plugin.app.vault.modify(o,p),new c.Notice(`Added ${t.basename} to "${r}" column in ${o.basename}`),!0}catch(n){return console.error("Error adding file to Kanban board:",n),this.plugin.settings.showDebugInfo&&console.error("Error details:",n),!1}}getCheckboxStateForColumn(t){if(!this.plugin.settings.enableCustomCheckboxStates)return"[ ]";let n=this.plugin.settings.kanbanColumnCheckboxMappings.find(e=>e.columnName.toLowerCase()===t.toLowerCase());return n?n.checkboxState:"[ ]"}updateCheckboxStatesInCard(t,n){if(!this.plugin.settings.enableCustomCheckboxStates)return t;let e=this.getCheckboxStateForColumn(n),s=t.split(`
`);if(s.length===0)return t;let o=/^(\s*- )\[[^\]]*\](.*)$/;if(o.test(s[0])){let a=s[0];s[0]=s[0].replace(o,(i,l,r)=>`${l}${e}${r}`),this.plugin.settings.showDebugInfo&&s[0]!==a&&console.log(`Updated checkbox states in card for column "${n}":`,{original:a,updated:s[0],targetState:e})}return s.join(`
`)}countTasksByCheckboxState(t){let n={},e=t.split(`
`);for(let s of e){let o=s.trim().match(/^- \[([^\]]*)\]/);if(o){let a=o[1];n[a]=(n[a]||0)+1}}return n}},D=class extends c.PluginSettingTab{constructor(t,n){super(t,n);this.plugin=n}display(){let{containerEl:t}=this;t.empty();let n=t.createDiv({cls:"dataview-status"});if(this.plugin.dvAPI?n.createEl("p",{text:"\u2705 Dataview API is available",cls:"dataview-available"}):(n.createEl("p",{text:"\u274C Dataview API is not available",cls:"dataview-unavailable"}),n.createEl("button",{text:"Check for Dataview",cls:"mod-cta"}).addEventListener("click",()=>{this.plugin.checkDataviewAPI(),this.plugin.dvAPI?(new c.Notice("Dataview API found!"),this.display()):new c.Notice("Dataview API not found. Make sure Dataview plugin is installed and enabled.")})),new c.Setting(t).setName("Show debug info").setDesc("Show debug information in the sidebar to help troubleshoot task counting issues").addToggle(e=>e.setValue(this.plugin.settings.showDebugInfo).onChange(async s=>{this.plugin.settings.showDebugInfo=s,await this.plugin.saveSettings(),this.app.workspace.getActiveFile()&&this.plugin.checkDataviewAPI()})),new c.Setting(t).setName("Animation").setHeading(),new c.Setting(t).setName("Show update animation").setDesc("Show a brief animation when updating the progress bar").addToggle(e=>e.setValue(this.plugin.settings.showUpdateAnimation).onChange(async s=>{this.plugin.settings.showUpdateAnimation=s,await this.plugin.saveSettings()})),new c.Setting(t).setName("Performance").setHeading(),new c.Setting(t).setName("Editor change delay").setDesc("Delay before updating after editor content changes (lower = more responsive, higher = better performance)").addSlider(e=>e.setLimits(100,1e3,50).setValue(this.plugin.settings.editorChangeDelay).setDynamicTooltip().onChange(async s=>{this.plugin.settings.editorChangeDelay=s,await this.plugin.saveSettings(),new c.Notice("Editor change delay updated. Restart plugin to apply changes.")})).addExtraButton(e=>e.setIcon("reset").setTooltip("Reset to default (500ms)").onClick(async()=>{this.plugin.settings.editorChangeDelay=500,await this.plugin.saveSettings(),this.display(),new c.Notice("Editor change delay reset. Restart plugin to apply changes.")})),new c.Setting(t).setName("Keyboard input delay").setDesc("Delay after keyboard input before updating progress (in milliseconds)").addSlider(e=>e.setLimits(100,1e3,50).setValue(this.plugin.settings.keyboardInputDelay).setDynamicTooltip().onChange(async s=>{this.plugin.settings.keyboardInputDelay=s,await this.plugin.saveSettings()})).addExtraButton(e=>e.setIcon("reset").setTooltip("Reset to default (100ms)").onClick(async()=>{this.plugin.settings.keyboardInputDelay=100,await this.plugin.saveSettings(),this.display()})),new c.Setting(t).setName("Checkbox click delay").setDesc("Delay after checkbox click before updating progress (in milliseconds)").addSlider(e=>e.setLimits(100,1e3,50).setValue(this.plugin.settings.checkboxClickDelay).setDynamicTooltip().onChange(async s=>{this.plugin.settings.checkboxClickDelay=s,await this.plugin.saveSettings()})).addExtraButton(e=>e.setIcon("reset").setTooltip("Reset to default (200ms)").onClick(async()=>{this.plugin.settings.checkboxClickDelay=200,await this.plugin.saveSettings(),this.display()})),new c.Setting(t).setName("Progress bar colors").setHeading(),new c.Setting(t).setName("Color scheme").setDesc("Choose a color scheme for the progress bar").addDropdown(e=>e.addOption("default","Default (Theme Colors)").addOption("red-orange-green","Red-Orange-Blue-Green").addOption("custom","Custom Colors").setValue(this.plugin.settings.progressColorScheme).onChange(async s=>{this.plugin.settings.progressColorScheme=s,s==="red-orange-green"&&(this.plugin.settings.lowProgressColor="#e06c75",this.plugin.settings.mediumProgressColor="#e5c07b",this.plugin.settings.highProgressColor="#61afef",this.plugin.settings.completeProgressColor="#98c379",this.plugin.settings.lowProgressThreshold=30,this.plugin.settings.mediumProgressThreshold=60,this.plugin.settings.highProgressThreshold=99,new c.Notice("Applied Red-Orange-Blue-Green color scheme")),await this.plugin.saveSettings(),this.display();let o=this.app.workspace.getActiveFile();o&&this.plugin.sidebarView&&this.plugin.sidebarView.updateProgressBar(o)})),this.plugin.settings.progressColorScheme==="custom"&&(new c.Setting(t).setName("Low progress color").setDesc(`Color for progress below ${this.plugin.settings.lowProgressThreshold}%`).addText(e=>e.setValue(this.plugin.settings.lowProgressColor).onChange(async s=>{this.plugin.settings.lowProgressColor=s,await this.plugin.saveSettings();let o=this.app.workspace.getActiveFile();o&&this.plugin.sidebarView&&this.plugin.sidebarView.updateProgressBar(o)})),new c.Setting(t).setName("Medium progress color").setDesc(`Color for progress between ${this.plugin.settings.lowProgressThreshold}% and ${this.plugin.settings.mediumProgressThreshold}%`).addText(e=>e.setValue(this.plugin.settings.mediumProgressColor).onChange(async s=>{this.plugin.settings.mediumProgressColor=s,await this.plugin.saveSettings();let o=this.app.workspace.getActiveFile();o&&this.plugin.sidebarView&&this.plugin.sidebarView.updateProgressBar(o)})),new c.Setting(t).setName("High progress color").setDesc(`Color for progress between ${this.plugin.settings.mediumProgressThreshold}% and ${this.plugin.settings.highProgressThreshold}%`).addText(e=>e.setValue(this.plugin.settings.highProgressColor).onChange(async s=>{this.plugin.settings.highProgressColor=s,await this.plugin.saveSettings();let o=this.app.workspace.getActiveFile();o&&this.plugin.sidebarView&&this.plugin.sidebarView.updateProgressBar(o)})),new c.Setting(t).setName("Complete progress color").setDesc("Color for 100% progress").addText(e=>e.setValue(this.plugin.settings.completeProgressColor).onChange(async s=>{this.plugin.settings.completeProgressColor=s,await this.plugin.saveSettings();let o=this.app.workspace.getActiveFile();o&&this.plugin.sidebarView&&this.plugin.sidebarView.updateProgressBar(o)})),new c.Setting(t).setName("Low progress threshold").setDesc("Percentage below which progress is considered low").addSlider(e=>e.setLimits(1,99,1).setValue(this.plugin.settings.lowProgressThreshold).setDynamicTooltip().onChange(async s=>{s>=this.plugin.settings.mediumProgressThreshold&&(s=this.plugin.settings.mediumProgressThreshold-1),this.plugin.settings.lowProgressThreshold=s,await this.plugin.saveSettings();let o=this.app.workspace.getActiveFile();o&&this.plugin.sidebarView&&this.plugin.sidebarView.updateProgressBar(o)})),new c.Setting(t).setName("Medium progress threshold").setDesc("Percentage below which progress is considered medium").addSlider(e=>e.setLimits(1,99,1).setValue(this.plugin.settings.mediumProgressThreshold).setDynamicTooltip().onChange(async s=>{s<=this.plugin.settings.lowProgressThreshold&&(s=this.plugin.settings.lowProgressThreshold+1),s>=this.plugin.settings.highProgressThreshold&&(s=this.plugin.settings.highProgressThreshold-1),this.plugin.settings.mediumProgressThreshold=s,await this.plugin.saveSettings();let o=this.app.workspace.getActiveFile();o&&this.plugin.sidebarView&&this.plugin.sidebarView.updateProgressBar(o)})),new c.Setting(t).setName("High progress threshold").setDesc("Percentage below which progress is considered high (but not complete)").addSlider(e=>e.setLimits(1,99,1).setValue(this.plugin.settings.highProgressThreshold).setDynamicTooltip().onChange(async s=>{s<=this.plugin.settings.mediumProgressThreshold&&(s=this.plugin.settings.mediumProgressThreshold+1),this.plugin.settings.highProgressThreshold=s,await this.plugin.saveSettings();let o=this.app.workspace.getActiveFile();o&&this.plugin.sidebarView&&this.plugin.sidebarView.updateProgressBar(o)}))),new c.Setting(t).setName("Interface").setHeading(),new c.Setting(t).setName("Max tabs height").setDesc("Maximum height for workspace tabs (e.g., 110px, 200px, auto)").addText(e=>(e.setValue(this.plugin.settings.maxTabsHeight),e.inputEl.addEventListener("blur",async()=>{let s=e.inputEl.value;s==="auto"||s==="none"||/^\d+(\.\d+)?(px|em|rem|vh|%)$/.test(s)?this.plugin.settings.maxTabsHeight!==s&&(this.plugin.settings.maxTabsHeight=s,await this.plugin.saveSettings(),new c.Notice(`Max tabs height updated to ${s}`)):(new c.Notice("Please enter 'auto', 'none' or a valid CSS length value (e.g., 110px)"),e.setValue(this.plugin.settings.maxTabsHeight))}),e.inputEl.addEventListener("keydown",async s=>{s.key==="Enter"&&(s.preventDefault(),e.inputEl.blur())}),e.inputEl.style.width="120px",e.inputEl.placeholder="auto",e)).addExtraButton(e=>e.setIcon("reset").setTooltip("Reset to default (auto)").onClick(async()=>{this.plugin.settings.maxTabsHeight="auto",await this.plugin.saveSettings(),this.display(),new c.Notice("Max tabs height reset to 'auto'")})),new c.Setting(t).setName("Metadata auto-update").setHeading(),new c.Setting(t).setName("Auto-update metadata").setDesc("Automatically update metadata when all tasks are completed").addToggle(e=>e.setValue(this.plugin.settings.autoUpdateMetadata).onChange(async s=>{this.plugin.settings.autoUpdateMetadata=s,await this.plugin.saveSettings(),this.display()})),this.plugin.settings.autoUpdateMetadata&&(new c.Setting(t).setName("Change status").setDesc("Change 'status: In Progress' to 'status: Completed' when tasks reach 100%").addToggle(e=>e.setValue(this.plugin.settings.autoChangeStatus).onChange(async s=>{this.plugin.settings.autoChangeStatus=s,await this.plugin.saveSettings()})),new c.Setting(t).setName("Update finished date").setDesc("Set 'finished: ' to today's date when tasks reach 100%").addToggle(e=>e.setValue(this.plugin.settings.autoUpdateFinishedDate).onChange(async s=>{this.plugin.settings.autoUpdateFinishedDate=s,await this.plugin.saveSettings()})),this.plugin.settings.autoChangeStatus&&(new c.Setting(t).setName("Todo status label").setDesc("Status label for files with 0% progress").addText(e=>e.setPlaceholder("Todo").setValue(this.plugin.settings.statusTodo).onChange(async s=>{this.plugin.settings.statusTodo=s,await this.plugin.saveSettings()})).addExtraButton(e=>e.setIcon("reset").setTooltip("Reset to default").onClick(async()=>{this.plugin.settings.statusTodo="Todo",await this.plugin.saveSettings(),this.display()})),new c.Setting(t).setName("In progress status label").setDesc("Status label for files with 1-99% progress").addText(e=>e.setPlaceholder("In Progress").setValue(this.plugin.settings.statusInProgress).onChange(async s=>{this.plugin.settings.statusInProgress=s,await this.plugin.saveSettings()})).addExtraButton(e=>e.setIcon("reset").setTooltip("Reset to default").onClick(async()=>{this.plugin.settings.statusInProgress="In Progress",await this.plugin.saveSettings(),this.display()})),new c.Setting(t).setName("Completed status label").setDesc("Status label for files with 100% progress").addText(e=>e.setPlaceholder("Completed").setValue(this.plugin.settings.statusCompleted).onChange(async s=>{this.plugin.settings.statusCompleted=s,await this.plugin.saveSettings()})).addExtraButton(e=>e.setIcon("reset").setTooltip("Reset to default").onClick(async()=>{this.plugin.settings.statusCompleted="Completed",await this.plugin.saveSettings(),this.display()}))),new c.Setting(t).setName("Kanban integration").setHeading(),new c.Setting(t).setName("Update Kanban boards").setDesc("Automatically move cards in Kanban boards based on task status").addToggle(e=>e.setValue(this.plugin.settings.autoUpdateKanban).onChange(async s=>{this.plugin.settings.autoUpdateKanban=s,await this.plugin.saveSettings(),this.display()})),this.plugin.settings.autoUpdateKanban)){new c.Setting(t).setName("Sync Kanban columns with status").setDesc("Match Kanban column names to status values (Todo, In Progress, Completed)").addToggle(s=>s.setValue(this.plugin.settings.kanbanSyncWithStatus).onChange(async o=>{this.plugin.settings.kanbanSyncWithStatus=o,await this.plugin.saveSettings(),this.display()})),this.plugin.settings.kanbanSyncWithStatus||new c.Setting(t).setName("Completed column name").setDesc("The name of the column where completed items should be moved to (e.g., 'Complete', 'Done', 'Finished')").addText(s=>s.setPlaceholder("Complete").setValue(this.plugin.settings.kanbanCompletedColumn).onChange(async o=>{this.plugin.settings.kanbanCompletedColumn=o,await this.plugin.saveSettings()})).addExtraButton(s=>s.setIcon("reset").setTooltip("Reset to default (Complete)").onClick(async()=>{this.plugin.settings.kanbanCompletedColumn="Complete",await this.plugin.saveSettings(),this.display()})),new c.Setting(t).setName("Auto-detect Kanban boards").setDesc("Automatically detect files that appear to be Kanban boards").addToggle(s=>s.setValue(this.plugin.settings.kanbanAutoDetect).onChange(async o=>{this.plugin.settings.kanbanAutoDetect=o,await this.plugin.saveSettings(),this.display()}));let e=t.createDiv({cls:"kanban-info",attr:{style:"background: var(--background-secondary-alt); padding: 10px; border-radius: 5px; margin-top: 10px;"}});e.createEl("p",{text:"\u2139\uFE0F Column naming tip:",attr:{style:"font-weight: bold; margin: 0 0 5px 0;"}}),e.createEl("p",{text:`To get the best results, name your Kanban columns to match the status values: "${this.plugin.settings.statusTodo}", "${this.plugin.settings.statusInProgress}", and "${this.plugin.settings.statusCompleted}".`,attr:{style:"margin: 0;"}})}if(new c.Setting(t).setName("Auto-add files to Kanban board").setDesc("Automatically add files with tasks to a specified Kanban board if they're not already there").addToggle(e=>e.setValue(this.plugin.settings.autoAddToKanban).onChange(async s=>{this.plugin.settings.autoAddToKanban=s,await this.plugin.saveSettings(),this.display()})),this.plugin.settings.autoAddToKanban&&(new c.Setting(t).setName("Target Kanban board").setDesc("The path to the Kanban board where files should be added").addText(o=>o.setPlaceholder("path/to/kanban.md").setValue(this.plugin.settings.autoAddKanbanBoard).onChange(async a=>{this.plugin.settings.autoAddKanbanBoard=a,await this.plugin.saveSettings()})),t.createEl("div",{text:"Select Kanban board file:",attr:{style:"margin-left: 36px; margin-bottom: 8px;"}}),t.createEl("div",{attr:{style:"margin-left: 36px; margin-bottom: 12px;"}}).createEl("button",{text:"Browse...",cls:"mod-cta"}).addEventListener("click",async()=>{try{let o=new I(this.app,this.plugin);o.onChooseItem=a=>{a&&(this.plugin.settings.autoAddKanbanBoard=a.path,this.plugin.saveSettings().then(()=>{this.display()}))},o.open()}catch(o){new c.Notice("Error opening file picker. Please enter the path manually."),console.error("File picker error:",o)}}),new c.Setting(t).setName("Target column").setDesc("The column where new files should be added (e.g., 'Todo', 'Backlog')").addText(o=>o.setPlaceholder("Todo").setValue(this.plugin.settings.autoAddKanbanColumn).onChange(async a=>{this.plugin.settings.autoAddKanbanColumn=a,await this.plugin.saveSettings()})).addExtraButton(o=>o.setIcon("reset").setTooltip("Reset to default (Todo)").onClick(async()=>{this.plugin.settings.autoAddKanbanColumn="Todo",await this.plugin.saveSettings(),this.display()}))),new c.Setting(t).setName("Custom Checkbox States").setDesc("Configure custom checkbox states for different Kanban columns").setHeading(),new c.Setting(t).setName("Enable custom checkbox states").setDesc("When enabled, cards will automatically update their checkbox states when moved between Kanban columns").addToggle(e=>e.setValue(this.plugin.settings.enableCustomCheckboxStates).onChange(async s=>{this.plugin.settings.enableCustomCheckboxStates=s,await this.plugin.saveSettings(),this.display()})),this.plugin.settings.enableCustomCheckboxStates){new c.Setting(t).setName("Enable Kanban card checkbox sync").setDesc("When enabled, dragging cards between Kanban columns will automatically update checkbox states of the cards in the Kanban board").addToggle(a=>a.setValue(this.plugin.settings.enableKanbanToFileSync).onChange(async i=>{this.plugin.settings.enableKanbanToFileSync=i,await this.plugin.saveSettings()})),new c.Setting(t).setName("Auto-sync checkbox states on Kanban open").setDesc("When enabled, automatically sync all card checkbox states to match their columns when opening a Kanban board (runs once per session for performance)").addToggle(a=>a.setValue(this.plugin.settings.enableKanbanAutoSync).onChange(async i=>{this.plugin.settings.enableKanbanAutoSync=i,await this.plugin.saveSettings()})),new c.Setting(t).setName("Protect custom checkbox states from Kanban normalization").setDesc("When enabled, prevents the Kanban plugin from automatically converting custom checkbox states (like [/], [~]) to standard states ([x]). This preserves your custom state mappings.").addToggle(a=>a.setValue(this.plugin.settings.enableKanbanNormalizationProtection).onChange(async i=>{this.plugin.settings.enableKanbanNormalizationProtection=i,await this.plugin.saveSettings()}));let e=t.createDiv({cls:"custom-checkbox-info",attr:{style:"background: var(--background-secondary-alt); padding: 10px; border-radius: 5px; margin: 10px 0;"}});e.createEl("p",{text:"\u2139\uFE0F Custom Checkbox States Configuration:",attr:{style:"font-weight: bold; margin: 0 0 5px 0;"}}),e.createEl("p",{text:"Define which checkbox state should be used for each Kanban column. Common states include: [ ] (todo), [/] (in progress), [x] (completed), [>] (forwarded), [-] (cancelled).",attr:{style:"margin: 0 0 5px 0;"}}),this.plugin.settings.kanbanColumnCheckboxMappings.forEach((a,i)=>{let l=t.createDiv({cls:"checkbox-mapping-container",attr:{style:"display: flex; gap: 10px; align-items: center; margin: 10px 0; padding: 10px; border: 1px solid var(--background-modifier-border); border-radius: 5px;"}}),r=l.createEl("input",{type:"text",value:a.columnName,attr:{placeholder:"Column Name",style:"flex: 1; padding: 5px;"}}),h=l.createEl("input",{type:"text",value:a.checkboxState,attr:{placeholder:"[ ]",style:"width: 60px; padding: 5px; text-align: center;"}}),g=l.createEl("button",{text:"Remove",cls:"mod-warning",attr:{style:"padding: 5px 10px;"}});r.addEventListener("change",async()=>{this.plugin.settings.kanbanColumnCheckboxMappings[i].columnName=r.value,await this.plugin.saveSettings()}),h.addEventListener("change",async()=>{this.plugin.settings.kanbanColumnCheckboxMappings[i].checkboxState=h.value,await this.plugin.saveSettings()}),g.addEventListener("click",async()=>{this.plugin.settings.kanbanColumnCheckboxMappings.splice(i,1),await this.plugin.saveSettings(),this.display()})}),t.createEl("button",{text:"Add Column Mapping",cls:"mod-cta",attr:{style:"margin: 10px 0;"}}).addEventListener("click",async()=>{this.plugin.settings.kanbanColumnCheckboxMappings.push({columnName:"",checkboxState:"[ ]"}),await this.plugin.saveSettings(),this.display()}),t.createEl("button",{text:"Reset to Defaults",cls:"mod-warning",attr:{style:"margin: 10px 0;"}}).addEventListener("click",async()=>{this.plugin.settings.kanbanColumnCheckboxMappings=[{columnName:"Todo",checkboxState:"[ ]"},{columnName:"In Progress",checkboxState:"[/]"},{columnName:"Complete",checkboxState:"[x]"},{columnName:"Done",checkboxState:"[x]"}],await this.plugin.saveSettings(),this.display()})}}},I=class extends c.SuggestModal{constructor(t,n){super(t);this.plugin=n,this.onChooseItem=()=>{}}getSuggestions(t){return this.app.vault.getMarkdownFiles().filter(s=>t?s.path.toLowerCase().includes(t.toLowerCase()):!0)}renderSuggestion(t,n){n.createEl("div",{text:t.path})}onChooseSuggestion(t,n){this.onChooseItem&&this.onChooseItem(t)}};

/* nosourcemap */