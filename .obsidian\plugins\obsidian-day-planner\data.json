{"snapStepMinutes": 5, "progressIndicator": "bar", "showTaskNotification": true, "zoomLevel": 2, "timelineIcon": "calendar-with-checkmark", "endLabel": "All done", "startHour": 6, "timelineDateFormat": "YYYY-MM-DD", "centerNeedle": false, "plannerHeading": "📌 今日学习安排", "plannerHeadingLevel": 2, "timelineColored": false, "timelineStartColor": "#006466", "timelineEndColor": "#4d194d", "timestampFormat": "HH:mm", "hourFormat": "HH", "dataviewSource": "", "extendDurationUntilNext": true, "defaultDurationMinutes": 30, "minimalDurationMinutes": 5, "showTimestampInTaskBlock": true, "showUncheduledTasks": true, "showUnscheduledNestedTasks": true, "showNow": true, "showNext": true, "pluginVersion": "0.28.0", "showCompletedTasks": true, "showSubtasksInTaskBlocks": true, "icals": [], "colorOverrides": [], "releaseNotes": true, "taskStatusOnCreation": "-", "eventFormatOnCreation": "task", "sortTasksInPlanAfterEdit": true, "firstDayOfWeek": "monday", "multiDayRange": "3-days", "showTimeTracker": false, "showActiveClocks": false, "rawIcals": []}