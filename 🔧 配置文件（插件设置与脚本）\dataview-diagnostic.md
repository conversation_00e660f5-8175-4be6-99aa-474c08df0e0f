# 🔧 Dataview查询诊断与修复指南

## 🚨 常见问题诊断

### 问题1: "PARSING FAILED" 错误

**症状**: 
- Dataview查询显示解析失败
- 错误信息包含 "Expected one of the following: 'and' or 'or'"

**原因**: 
- 标签查询语法错误，特别是包含中文字符的标签
- 使用了 `#"标签名"` 格式在某些情况下会失败

**解决方案**:
```dataview
// ❌ 错误写法
FROM #每日记录 and #"项目名"

// ✅ 正确写法
FROM "具体文件路径"
WHERE contains(tags, "每日记录")
```

### 问题2: DataviewJS查询返回空结果

**症状**:
- DataviewJS代码块不显示任何内容
- 或显示"未找到记录"

**原因**:
- 标签检查方式错误
- 路径不正确

**解决方案**:
```javascript
// ❌ 错误写法
const records = dv.pages('#标签名');

// ✅ 正确写法
const records = dv.pages('"文件夹路径"').where(p => p.tags && p.tags.includes("标签名"));
```

## 🛠️ 修复步骤

### 步骤1: 检查文件结构
确保以下文件夹存在：
```
🎓 study/📌 学习项目/
├── 每日记录/
│   └── {项目名称}/
│       └── YYYY-MM-DD.md
└── 进度仪表板/
    └── {项目名称}-进度仪表板.md
```

### 步骤2: 检查YAML frontmatter格式
每日记录文件应包含：
```yaml
---
日期: YYYY-MM-DD
项目: 项目名称
学习时长: 数字
完成度: 数字
效率评分: 数字
tags:
  - 每日记录
  - 项目进度
  - 项目名称
---
```

### 步骤3: 更新查询语法
将所有Dataview查询更新为新格式：

**标准Dataview查询**:
```dataview
TABLE 字段1, 字段2
FROM "文件夹路径"
WHERE contains(tags, "标签名")
SORT 字段 DESC
```

**DataviewJS查询**:
```javascript
const pages = dv.pages('"文件夹路径"')
  .where(p => p.tags && p.tags.includes("标签名"));
```

## 🔍 自动检查清单

### 检查项目仪表板
- [ ] 打开 `🎓 study/📌 学习项目/进度仪表板/` 文件夹
- [ ] 逐个检查仪表板文件是否有错误
- [ ] 如有错误，按照上述格式修复

### 检查项目文件
- [ ] 打开 `🎓 study/📌 学习项目/` 文件夹
- [ ] 检查项目文件中的dataviewjs查询
- [ ] 更新查询语法

### 检查QuickAdd脚本
- [ ] 检查 `quickadd-progress-dashboard.js`
- [ ] 确保模板使用正确的查询语法

## 📋 快速修复模板

### 仪表板查询模板
```dataview
TABLE 
    学习时长 + "min" as "时长",
    效率评分 + "/10" as "效率",
    完成度 + "%" as "完成度",
    file.link as "详细记录"
FROM "🎓 study/📌 学习项目/每日记录/项目名称"
WHERE contains(tags, "每日记录")
SORT 日期 DESC
LIMIT 10
```

### DataviewJS统计模板
```javascript
const dailyRecords = dv.pages('"🎓 study/📌 学习项目/每日记录/项目名称"')
  .where(p => p.tags && p.tags.includes("每日记录"))
  .sort(p => p.日期);

if (dailyRecords.length > 0) {
    // 统计逻辑
    const totalTime = dailyRecords.map(p => p.学习时长 || 0).reduce((a, b) => a + b, 0);
    dv.paragraph(`总学习时长: ${totalTime}分钟`);
} else {
    dv.paragraph("暂无学习记录");
}
```

## 🎯 预防措施

1. **统一标签格式**: 使用英文标签或确保中文标签格式一致
2. **路径查询优先**: 优先使用文件路径而非标签查询
3. **定期检查**: 每周检查一次Dataview查询是否正常工作
4. **备份重要查询**: 将工作正常的查询保存为模板

---
*诊断指南更新时间: 2025-07-31*
