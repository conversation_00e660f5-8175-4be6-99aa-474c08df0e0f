.mini-timeline.svelte-16282mn{position:relative;display:flex;gap:0;align-items:stretch;height:100%;background-color:var(--color-base-30)}.time-marker.svelte-16282mn{position:absolute;z-index:2;width:0;height:0;border-right:var(--time-marker-half-width-px) solid transparent;border-left:var(--time-marker-half-width-px) solid transparent}.time-marker.top.svelte-16282mn{top:var(--time-marker-offset-y-px);border-top:var(--time-marker-half-width-px) solid var(--color-accent);border-top-left-radius:2px;border-top-right-radius:2px}.time-marker.bottom.svelte-16282mn{bottom:var(--time-marker-offset-y-px);border-bottom:var(--time-marker-half-width-px) solid var(--color-accent);border-bottom-right-radius:2px;border-bottom-left-radius:2px}.hour-segment.svelte-16282mn{position:relative;top:-15%;width:10px;height:130%;background-color:transparent}.hour-segment.svelte-16282mn:not(:last-child){border-right:1px solid var(--text-faint)}.hour-end-segment.svelte-16282mn:not(:last-child){top:-20%;left:1px;height:140%;border-right:2px solid var(--text-faint)}.mini-time-block-wrapper.svelte-16282mn{position:absolute;z-index:1;top:0;right:0;bottom:0;left:0;overflow:hidden}.mini-time-block.svelte-16282mn{position:absolute;display:flex;align-items:center;background-color:var(--background-primary);border:1px solid var(--text-faint)}.remote-block-strip.svelte-16282mn{flex:1 0 0;height:30%}.root.svelte-1xmfwal{display:contents}.root.svelte-1xmfwal .status-bar-item-icon{display:inline-flex}.status-bar-item-segment.svelte-1xmfwal{display:flex;gap:var(--size-2-1);align-items:center}.status-bar-item-segment.progress-pie.svelte-1xmfwal{display:block}.pill-icon{width:var(--size-4-3);height:var(--size-4-3)}.pill.svelte-w634mr{display:inline-flex;gap:var(--size-4-1);align-items:center;padding:var(--size-2-1) var(--size-4-2);font-size:var(--font-ui-smaller);color:var(--tag-color);white-space:nowrap;background-color:var(--tag-background);border-radius:var(--radius-m)}.view-header-nav-buttons.svelte-zd0ihh{display:flex;gap:var(--size-4-1)}.search-results-scroller.svelte-1jzfqa{overflow-y:auto}.search-results.svelte-1jzfqa{display:flex;flex-direction:column;margin:var(--size-4-2) var(--size-4-3);background-color:var( --search-results-bg-color, var(--background-secondary) );border:1px solid var(--background-modifier-border);border-radius:var(--radius-s)}.search-result.svelte-1jzfqa:not(:last-child){border-bottom:1px solid var(--background-modifier-border)}.day-planner-task-decoration{margin:0 .25em;padding:.1em .25em;font-size:var(--tag-size);font-weight:var(--tag-weight);line-height:1;color:var(--tag-color);text-decoration:var(--tag-decoration);background-color:var(--tag-background);border-radius:var(--radius-s)}.rendered-markdown.svelte-16dqlil{--checkbox-size: var(--font-ui-small);flex:1 0 0;padding:var(--size-2-1) var(--size-4-1);color:var(--text-normal)}.rendered-markdown.svelte-16dqlil p,.rendered-markdown.svelte-16dqlil ul{margin-block:0}.rendered-markdown.svelte-16dqlil ul,.rendered-markdown.svelte-16dqlil ol{padding-inline-start:20px}.rendered-markdown.svelte-16dqlil input[type=checkbox]{top:2px;margin-inline-end:4px;border-color:var(--text-muted)}.rendered-markdown.svelte-16dqlil li{color:var(--text-normal)}.rendered-markdown.svelte-16dqlil li.task-list-item[data-task=x],.rendered-markdown.svelte-16dqlil li.task-list-item[data-task=X]{color:var(--text-muted)}.padding.svelte-k3w6xi{position:var(--time-block-position, static);top:var(--time-block-top, 0);left:var(--time-block-left, 0);display:flex;grid-column:var(--time-block-grid-column, unset);width:var(--time-block-width, 100%);height:var(--time-block-height, auto);padding:var(--time-block-padding, 0 1px 2px);transition:.05s linear}.content.svelte-k3w6xi{--default-box-shadow: 1px 1px 2px 0 #0000001f;position:relative;flex:1 0 0;font-size:var(--font-ui-small);text-align:left;overflow-wrap:anywhere;white-space:normal;border:1px solid var(--time-block-border-color, var(--color-base-50));border-radius:var(--radius-s);box-shadow:var(--time-block-box-shadow, var(--default-box-shadow))}.truncated-left.svelte-k3w6xi{border-left-style:dashed;border-left-width:2px;border-top-left-radius:0;border-bottom-left-radius:0}.truncated-right.svelte-k3w6xi{border-right-style:dashed;border-right-width:2px;border-top-right-radius:0;border-bottom-right-radius:0}input.svelte-q2gfbz{flex-shrink:0;margin:var(--size-4-2) var(--size-4-3) 0}.result-message.svelte-q2gfbz{margin:var(--size-4-2) var(--size-4-3);font-size:var(--font-ui-smaller);color:var(--text-faint)}.search-wrapper.svelte-q2gfbz{display:flex;flex-direction:column;max-height:var(--search-max-height, 100%)}.clickable-icon.svelte-6wn7nv{color:var(--color, var(--icon-color));white-space:nowrap;border:var(--control-button-border, none);border-radius:var(--border-radius, var(--clickable-icon-radius))}.hours-container.svelte-1xa1xit{position:sticky;z-index:5;left:0;display:flex;flex-direction:column;height:fit-content;background-color:var(--background-primary);border-right:1px solid var(--background-modifier-border);box-shadow:var(--ruler-box-shadow, none)}.hour.svelte-1xa1xit{display:flex;flex:1 0 0;flex-direction:row-reverse;padding-inline:var(--size-4-2);font-size:var(--font-ui-smaller);font-weight:var(--font-semibold);color:var(--text-muted);border-bottom:1px solid var(--background-modifier-border)}.scroller.svelte-f6k6y4{overflow:auto;display:flex;flex:1 0 0;background-color:var(--background-secondary)}.callout-wrapper.svelte-11l0vy6 .planner-callout-icon{color:rgb(var(--callout-color))}.callout-wrapper.svelte-11l0vy6{--callout-opacity: .1;display:flex;gap:var(--size-4-1);padding:var(--size-4-2);color:rgb(var(--callout-color));background-color:rgba(var(--callout-color),var(--callout-opacity));border-radius:var(--radius-s)}.callout-wrapper.svelte-11l0vy6 .svg-icon{flex-shrink:0}.setting-item.svelte-ysxaf1{padding:var(--size-2-3) 0}.setting-item-name.svelte-ysxaf1{font-size:var(--font-ui-small)}.dataview-source.svelte-1elgh5f{display:flex;flex-direction:column;gap:var(--size-4-2);font-size:var(--font-ui-small);color:var(--text-muted)}.dataview-source.svelte-1elgh5f input:where(.svelte-1elgh5f){font-family:var(--font-monospace)}.error-message.svelte-1elgh5f{overflow-x:auto;margin-block:0;padding:var(--size-4-1);border:1px solid var(--text-error);border-radius:var(--radius-s)}.controls-section.svelte-1elgh5f{margin:var(--size-4-2) 0;font-size:var(--font-ui-small);font-weight:var(--font-medium)}.column.svelte-456hjm{position:relative;flex:1 0 var(--timeline-flex-basis);height:fit-content;background-color:var( --column-background-color, var(--background-secondary) );border-right:1px solid var(--background-modifier-border)}.hour-block.svelte-456hjm{flex:1 0 0;border-bottom:1px solid var(--background-modifier-border)}.half-hour-separator.svelte-456hjm{border-bottom:1px dashed var(--background-modifier-border)}.needle.svelte-15i5oy0{height:2px;background-color:var(--color-accent)}.ball.svelte-15i5oy0{position:absolute;z-index:1000;width:var(--size-4-1);height:12px;margin-top:-5px;background:var(--color-accent);border-radius:var(--radius-s)}.location.svelte-1kjgyi7,.rendered-description.svelte-1kjgyi7{font-size:var(--font-ui-smaller);color:var(--text-muted)}.rendered-description.svelte-1kjgyi7 :is(ul,ol){margin:.2em;padding-inline-start:20px}.calendar-name.svelte-1kjgyi7{color:var(--text-muted)}.remote-task-content.svelte-1kjgyi7{display:flex;flex:1 0 0;flex-direction:column;height:100%;padding:var(--size-2-1) var(--size-4-1);padding-bottom:0;padding-left:calc(4px + var(--size-4-2));color:var(--text-normal)}.ribbon.svelte-1kjgyi7{position:absolute;top:var(--size-2-1);bottom:var(--size-2-1);left:var(--size-2-1);width:var(--size-4-1);background-color:var(--ribbon-color);border-radius:var(--radius-s)}.declined.svelte-1kjgyi7{background-color:inherit;border-right:2px solid var(--ribbon-color)}.ribbon.tentative.svelte-1kjgyi7{background:repeating-linear-gradient(45deg,var(--ribbon-color),var(--ribbon-color) 5px,transparent 5px,transparent 10px);border-right:1px solid var(--ribbon-color)}.summary.declined.svelte-1kjgyi7{color:var(--text-muted);text-decoration:line-through}.clickable-icon.svelte-1hn2r71{padding:var(--size-2-2) var(--size-4-2)}.expanded-wrapper.svelte-6s789o,.expanding-controls.svelte-6s789o{display:flex;gap:var(--size-2-1)}.expanding-controls.svelte-6s789o{position:var(--expanding-controls-position, static);right:0;bottom:0;padding:var(--size-2-1);background-color:var(--background-primary);border:1px solid var(--background-modifier-border);border-radius:var(--size-4-1);box-shadow:var(--floating-controls-shadow)}.floating-ui.svelte-13xxfh4{position:absolute;z-index:9999;top:0;left:0;width:max-content}.tasks.svelte-1gx3ymr{top:0;bottom:0;display:flex;flex-direction:column;margin-inline:var(--size-4-2)}.column-tracks-wrapper.svelte-2ckn2p{position:absolute;z-index:-1;inset:0 var(--scrollbar-width) 0 0;overflow-x:hidden;display:grid;grid-template-columns:repeat(var(--column-count),minmax(var(--cell-flex-basis),1fr))}.border.svelte-2ckn2p{border-right:1px solid var(--background-modifier-border)}.multi-day-row.svelte-koteyy{position:relative;display:grid;grid-auto-flow:column;grid-template-columns:repeat(var(--column-count),minmax(var(--cell-flex-basis),1fr));flex:1 0 0;align-self:flex-start}.dp-header-row{--cell-flex-basis: calc( var(--timeline-flex-basis) * var(--timeline-internal-column-count, 1) );z-index:1000;overflow-x:hidden;display:flex;box-shadow:var(--shadow-bottom)}.ruler.svelte-1poff0m{z-index:500;overflow-y:hidden;grid-area:ruler;box-shadow:var(--shadow-bottom)}.scrollbar-filler.svelte-1poff0m{height:var(--scrollbar-width)}.horizontal-grip{flex:0 0 auto;color:var(--icon-color);opacity:var(--icon-opacity)}.horizontal-grip:hover{cursor:grab;opacity:var(--icon-opacity-hover)}.multi-day-row-wrapper.svelte-1poff0m{position:relative;overflow:hidden scroll;display:flex;flex:1 0 0}.day-buttons.svelte-1poff0m{grid-area:dates;font-size:var(--font-ui-small)}.horizontal-resize-box-wrapper{position:relative;grid-area:multiday;min-height:3vh;max-height:20vh;border-bottom:1px solid var(--background-modifier-border)}.controls-sidebar.svelte-1poff0m{position:absolute;top:0;right:var(--scrollbar-width);display:flex;flex-direction:column;gap:var(--size-4-2);align-self:flex-start;padding:var(--size-4-2) var(--size-4-1);background-color:var(--background-primary);border-bottom:1px solid var(--background-modifier-border);border-left:1px solid var(--background-modifier-border);border-bottom-left-radius:var(--radius-m);box-shadow:var(--input-shadow)}.multi-day-main-content.svelte-1poff0m{position:relative;display:flex;grid-area:timelines;flex-direction:column}.corner.svelte-1poff0m{z-index:1000;display:flex;grid-area:corner;flex-direction:column-reverse;align-items:center;background-color:var(--background-primary);border-right:1px solid var(--background-modifier-border);border-bottom:1px solid var(--background-modifier-border);box-shadow:var(--shadow-bottom)}.header-cell.svelte-1poff0m{overflow-x:hidden;flex:1 0 var(--cell-flex-basis);width:var(--cell-flex-basis);font-weight:var(--font-semibold);background-color:var(--background-primary);border-right:1px solid var(--background-modifier-border);border-bottom:1px solid var(--background-modifier-border)}.header-cell.svelte-1poff0m:last-of-type{flex:1 0 calc(var(--cell-flex-basis) + var(--scrollbar-width));border-right:none}.side-controls-wrapper.svelte-1poff0m{grid-area:settings;width:min(320px,50vw);padding-inline:var(--size-4-3);border-left:1px solid var(--background-modifier-border)}.properties-wrapper.svelte-zowwmd{display:flex;gap:var(--size-4-1);align-items:center;padding:var(--size-4-1)}.tree-container.svelte-1yd9zwp{display:flex;flex:var(--flex);flex-direction:column}.tree-item-inner.svelte-1yd9zwp{font-weight:var(--font-medium)}.tree-item-self.svelte-1yd9zwp{margin-bottom:0;border-radius:0}:not(#dummy).workspace-leaf-resize-handle.svelte-4a85j2{cursor:row-resize;right:0;bottom:0;left:0;height:var(--divider-width-hover);border-bottom-width:var(--divider-width)}.resizeable-box.svelte-38wfvx{min-height:var(--icon-size);max-height:25vh}.active-filter{color:var(--text-success)}.today,.today:hover{background-color:var(--color-accent)}.date.svelte-1llou0s{display:flex;align-items:center;justify-content:center;font-size:var(--font-ui-small);font-weight:var(--font-medium);color:var(--text-normal)}.mod-error{color:var(--text-error)}.buttons-left.svelte-1llou0s{display:flex}.header.svelte-1llou0s,.buttons-left.svelte-1llou0s{gap:var(--size-2-1)}.header.svelte-1llou0s{display:grid;grid-template-columns:1fr auto 1fr}.header.svelte-1llou0s>*:last-child{justify-self:end}.controls.svelte-1llou0s{overflow:hidden;display:flex;flex:0 0 auto;flex-direction:column;gap:var(--size-4-2);padding:var(--size-4-2) var(--size-4-3);font-size:var(--font-ui-small)}.settings-wrapper.svelte-1llou0s{display:flex;flex-direction:column;gap:var(--size-4-2)}.controls.svelte-167mjhn{position:relative;z-index:1000;box-shadow:var(--shadow-bottom)}.controls.svelte-167mjhn>*{border-bottom:1px solid var(--background-modifier-border)}.unscheduled-task-container{overflow:auto}.edit-prompt.svelte-167mjhn{display:flex;flex-direction:column;align-items:center;padding:var(--size-4-2);font-size:var(--font-ui-small);color:var(--text-faint)}/*! 
 * OverlayScrollbars
 * Version: 2.10.0
 * 
 * Copyright (c) Rene Haas | KingSora.
 * https://github.com/KingSora
 * 
 * Released under the MIT license.
 */.os-size-observer,.os-size-observer-listener{scroll-behavior:auto!important;direction:inherit;pointer-events:none;overflow:hidden;visibility:hidden;box-sizing:border-box}.os-size-observer,.os-size-observer-listener,.os-size-observer-listener-item,.os-size-observer-listener-item-final{writing-mode:horizontal-tb;position:absolute;left:0;top:0}.os-size-observer{z-index:-1;contain:strict;display:flex;flex-direction:row;flex-wrap:nowrap;padding:inherit;border:inherit;box-sizing:inherit;margin:-133px;top:0;right:0;bottom:0;left:0;transform:scale(.1)}.os-size-observer:before{content:"";flex:none;box-sizing:inherit;padding:10px;width:10px;height:10px}.os-size-observer-appear{animation:os-size-observer-appear-animation 1ms forwards}.os-size-observer-listener{box-sizing:border-box;position:relative;flex:auto;padding:inherit;border:inherit;margin:-133px;transform:scale(10)}.os-size-observer-listener.ltr{margin-right:-266px;margin-left:0}.os-size-observer-listener.rtl{margin-left:-266px;margin-right:0}.os-size-observer-listener:empty:before{content:"";width:100%;height:100%}.os-size-observer-listener:empty:before,.os-size-observer-listener>.os-size-observer-listener-item{display:block;position:relative;padding:inherit;border:inherit;box-sizing:content-box;flex:auto}.os-size-observer-listener-scroll{box-sizing:border-box;display:flex}.os-size-observer-listener-item{right:0;bottom:0;overflow:hidden;direction:ltr;flex:none}.os-size-observer-listener-item-final{transition:none}@keyframes os-size-observer-appear-animation{0%{cursor:auto}to{cursor:none}}.os-trinsic-observer{flex:none;box-sizing:border-box;position:relative;max-width:0px;max-height:1px;padding:0;margin:0;border:none;overflow:hidden;z-index:-1;height:0;top:calc(100% + 1px);contain:strict}.os-trinsic-observer:not(:empty){height:calc(100% + 1px);top:-1px}.os-trinsic-observer:not(:empty)>.os-size-observer{width:1000%;height:1000%;min-height:1px;min-width:1px}[data-overlayscrollbars-initialize],[data-overlayscrollbars-viewport~=scrollbarHidden]{scrollbar-width:none!important}[data-overlayscrollbars-initialize]::-webkit-scrollbar,[data-overlayscrollbars-initialize]::-webkit-scrollbar-corner,[data-overlayscrollbars-viewport~=scrollbarHidden]::-webkit-scrollbar,[data-overlayscrollbars-viewport~=scrollbarHidden]::-webkit-scrollbar-corner{-webkit-appearance:none!important;-moz-appearance:none!important;appearance:none!important;display:none!important;width:0!important;height:0!important}[data-overlayscrollbars-initialize]:not([data-overlayscrollbars]):not(html):not(body){overflow:auto}html[data-overlayscrollbars-body]{overflow:hidden}html[data-overlayscrollbars-body],html[data-overlayscrollbars-body]>body{width:100%;height:100%;margin:0}html[data-overlayscrollbars-body]>body{overflow:visible;margin:0}[data-overlayscrollbars]{position:relative}[data-overlayscrollbars~=host],[data-overlayscrollbars-padding]{display:flex;align-items:stretch!important;flex-direction:row!important;flex-wrap:nowrap!important;scroll-behavior:auto!important}[data-overlayscrollbars-padding],[data-overlayscrollbars-viewport]:not([data-overlayscrollbars]){box-sizing:inherit;position:relative;flex:auto!important;height:auto;width:100%;min-width:0;padding:0;margin:0;border:none;z-index:0}[data-overlayscrollbars-viewport]:not([data-overlayscrollbars]){--os-vaw: 0;--os-vah: 0;outline:none}[data-overlayscrollbars-viewport]:not([data-overlayscrollbars]):focus{outline:none}[data-overlayscrollbars-viewport][data-overlayscrollbars-viewport~=arrange]:before{content:"";position:absolute;pointer-events:none;z-index:-1;min-width:1px;min-height:1px;width:var(--os-vaw);height:var(--os-vah)}[data-overlayscrollbars],[data-overlayscrollbars-padding],[data-overlayscrollbars-viewport]{overflow:hidden!important}[data-overlayscrollbars~=noClipping],[data-overlayscrollbars-padding~=noClipping]{overflow:visible!important}[data-overlayscrollbars-viewport~=measuring]{overflow:hidden!important;scroll-behavior:auto!important;scroll-snap-type:none!important}[data-overlayscrollbars-viewport~=overflowXVisible]:not([data-overlayscrollbars-viewport~=measuring]){overflow-x:visible!important}[data-overlayscrollbars-viewport~=overflowXHidden]{overflow-x:hidden!important}[data-overlayscrollbars-viewport~=overflowXScroll]{overflow-x:scroll!important}[data-overlayscrollbars-viewport~=overflowYVisible]:not([data-overlayscrollbars-viewport~=measuring]){overflow-y:visible!important}[data-overlayscrollbars-viewport~=overflowYHidden]{overflow-y:hidden!important}[data-overlayscrollbars-viewport~=overflowYScroll]{overflow-y:scroll!important}[data-overlayscrollbars-viewport~=noContent]:not(#osFakeId){font-size:0!important;line-height:0!important}[data-overlayscrollbars-viewport~=noContent]:not(#osFakeId):before,[data-overlayscrollbars-viewport~=noContent]:not(#osFakeId):after,[data-overlayscrollbars-viewport~=noContent]:not(#osFakeId)>*{display:none!important;position:absolute!important;width:1px!important;height:1px!important;padding:0!important;margin:-1px!important;overflow:hidden!important;clip:rect(0,0,0,0)!important;white-space:nowrap!important;border-width:0!important}[data-overlayscrollbars-viewport~=scrolling]{scroll-behavior:auto!important;scroll-snap-type:none!important}[data-overlayscrollbars-content]{box-sizing:inherit}[data-overlayscrollbars-contents]:not(#osFakeId):not([data-overlayscrollbars-padding]):not([data-overlayscrollbars-viewport]):not([data-overlayscrollbars-content]){display:contents}[data-overlayscrollbars-grid],[data-overlayscrollbars-grid] [data-overlayscrollbars-padding]{display:grid;grid-template:1fr/1fr}[data-overlayscrollbars-grid]>[data-overlayscrollbars-padding],[data-overlayscrollbars-grid]>[data-overlayscrollbars-viewport],[data-overlayscrollbars-grid]>[data-overlayscrollbars-padding]>[data-overlayscrollbars-viewport]{height:auto!important;width:auto!important}@property --os-scroll-percent{syntax: "<number>"; inherits: true; initial-value: 0;}@property --os-viewport-percent{syntax: "<number>"; inherits: true; initial-value: 0;}.os-scrollbar{--os-viewport-percent: 0;--os-scroll-percent: 0;--os-scroll-direction: 0;--os-scroll-percent-directional: calc( var(--os-scroll-percent) - (var(--os-scroll-percent) + (1 - var(--os-scroll-percent)) * -1) * var(--os-scroll-direction) )}.os-scrollbar{contain:size layout;contain:size layout style;transition:opacity .15s,visibility .15s,top .15s,right .15s,bottom .15s,left .15s;pointer-events:none;position:absolute;opacity:0;visibility:hidden}body>.os-scrollbar{position:fixed;z-index:99999}.os-scrollbar-transitionless{transition:none!important}.os-scrollbar-track{position:relative;padding:0!important;border:none!important}.os-scrollbar-handle{position:absolute}.os-scrollbar-track,.os-scrollbar-handle{pointer-events:none;width:100%;height:100%}.os-scrollbar.os-scrollbar-track-interactive .os-scrollbar-track,.os-scrollbar.os-scrollbar-handle-interactive .os-scrollbar-handle{pointer-events:auto;touch-action:none}.os-scrollbar-horizontal{bottom:0;left:0}.os-scrollbar-vertical{top:0;right:0}.os-scrollbar-rtl.os-scrollbar-horizontal{right:0}.os-scrollbar-rtl.os-scrollbar-vertical{right:auto;left:0}.os-scrollbar-visible{opacity:1;visibility:visible}.os-scrollbar-auto-hide.os-scrollbar-auto-hide-hidden{opacity:0;visibility:hidden}.os-scrollbar-interaction.os-scrollbar-visible{opacity:1;visibility:visible}.os-scrollbar-unusable,.os-scrollbar-unusable *,.os-scrollbar-wheel,.os-scrollbar-wheel *{pointer-events:none!important}.os-scrollbar-unusable .os-scrollbar-handle{opacity:0!important;transition:none!important}.os-scrollbar-horizontal .os-scrollbar-handle{bottom:0;left:calc(var(--os-scroll-percent-directional) * 100%);transform:translate(calc(var(--os-scroll-percent-directional) * -100%));width:calc(var(--os-viewport-percent) * 100%)}.os-scrollbar-vertical .os-scrollbar-handle{right:0;top:calc(var(--os-scroll-percent-directional) * 100%);transform:translateY(calc(var(--os-scroll-percent-directional) * -100%));height:calc(var(--os-viewport-percent) * 100%)}@supports (container-type: size){.os-scrollbar-track{container-type:size}.os-scrollbar-horizontal .os-scrollbar-handle{left:auto;transform:translate(calc(var(--os-scroll-percent-directional) * 100cqw + var(--os-scroll-percent-directional) * -100%))}.os-scrollbar-vertical .os-scrollbar-handle{top:auto;transform:translateY(calc(var(--os-scroll-percent-directional) * 100cqh + var(--os-scroll-percent-directional) * -100%))}.os-scrollbar-rtl.os-scrollbar-horizontal .os-scrollbar-handle{right:auto;left:0}}.os-scrollbar-rtl.os-scrollbar-vertical .os-scrollbar-handle{right:auto;left:0}.os-scrollbar.os-scrollbar-horizontal.os-scrollbar-cornerless,.os-scrollbar.os-scrollbar-horizontal.os-scrollbar-cornerless.os-scrollbar-rtl{left:0;right:0}.os-scrollbar.os-scrollbar-vertical.os-scrollbar-cornerless,.os-scrollbar.os-scrollbar-vertical.os-scrollbar-cornerless.os-scrollbar-rtl{top:0;bottom:0}@media print{.os-scrollbar{display:none}}.os-scrollbar{--os-size: 0;--os-padding-perpendicular: 0;--os-padding-axis: 0;--os-track-border-radius: 0;--os-track-bg: none;--os-track-bg-hover: none;--os-track-bg-active: none;--os-track-border: none;--os-track-border-hover: none;--os-track-border-active: none;--os-handle-border-radius: 0;--os-handle-bg: none;--os-handle-bg-hover: none;--os-handle-bg-active: none;--os-handle-border: none;--os-handle-border-hover: none;--os-handle-border-active: none;--os-handle-min-size: 33px;--os-handle-max-size: none;--os-handle-perpendicular-size: 100%;--os-handle-perpendicular-size-hover: 100%;--os-handle-perpendicular-size-active: 100%;--os-handle-interactive-area-offset: 0}.os-scrollbar-track{border:var(--os-track-border);border-radius:var(--os-track-border-radius);background:var(--os-track-bg);transition:opacity .15s,background-color .15s,border-color .15s}.os-scrollbar-track:hover{border:var(--os-track-border-hover);background:var(--os-track-bg-hover)}.os-scrollbar-track:active{border:var(--os-track-border-active);background:var(--os-track-bg-active)}.os-scrollbar-handle{border:var(--os-handle-border);border-radius:var(--os-handle-border-radius);background:var(--os-handle-bg)}.os-scrollbar-handle:hover{border:var(--os-handle-border-hover);background:var(--os-handle-bg-hover)}.os-scrollbar-handle:active{border:var(--os-handle-border-active);background:var(--os-handle-bg-active)}.os-scrollbar-track:before,.os-scrollbar-handle:before{content:"";position:absolute;left:0;right:0;top:0;bottom:0;display:block}.os-scrollbar-horizontal{padding:var(--os-padding-perpendicular) var(--os-padding-axis);right:var(--os-size);height:var(--os-size)}.os-scrollbar-horizontal.os-scrollbar-rtl{left:var(--os-size);right:0}.os-scrollbar-horizontal .os-scrollbar-track:before{top:calc(var(--os-padding-perpendicular) * -1);bottom:calc(var(--os-padding-perpendicular) * -1)}.os-scrollbar-horizontal .os-scrollbar-handle{min-width:var(--os-handle-min-size);max-width:var(--os-handle-max-size);height:var(--os-handle-perpendicular-size);transition:opacity .15s,background-color .15s,border-color .15s,height .15s}.os-scrollbar-horizontal .os-scrollbar-handle:before{top:calc((var(--os-padding-perpendicular) + var(--os-handle-interactive-area-offset)) * -1);bottom:calc(var(--os-padding-perpendicular) * -1)}.os-scrollbar-horizontal:hover .os-scrollbar-handle{height:var(--os-handle-perpendicular-size-hover)}.os-scrollbar-horizontal:active .os-scrollbar-handle{height:var(--os-handle-perpendicular-size-active)}.os-scrollbar-vertical{padding:var(--os-padding-axis) var(--os-padding-perpendicular);bottom:var(--os-size);width:var(--os-size)}.os-scrollbar-vertical .os-scrollbar-track:before{left:calc(var(--os-padding-perpendicular) * -1);right:calc(var(--os-padding-perpendicular) * -1)}.os-scrollbar-vertical .os-scrollbar-handle{min-height:var(--os-handle-min-size);max-height:var(--os-handle-max-size);width:var(--os-handle-perpendicular-size);transition:opacity .15s,background-color .15s,border-color .15s,width .15s}.os-scrollbar-vertical .os-scrollbar-handle:before{left:calc((var(--os-padding-perpendicular) + var(--os-handle-interactive-area-offset)) * -1);right:calc(var(--os-padding-perpendicular) * -1)}.os-scrollbar-vertical.os-scrollbar-rtl .os-scrollbar-handle:before{right:calc((var(--os-padding-perpendicular) + var(--os-handle-interactive-area-offset)) * -1);left:calc(var(--os-padding-perpendicular) * -1)}.os-scrollbar-vertical:hover .os-scrollbar-handle{width:var(--os-handle-perpendicular-size-hover)}.os-scrollbar-vertical:active .os-scrollbar-handle{width:var(--os-handle-perpendicular-size-active)}[data-overlayscrollbars-viewport~=measuring]>.os-scrollbar,.os-theme-none.os-scrollbar{display:none!important}.os-theme-dark,.os-theme-light{box-sizing:border-box;--os-size: 10px;--os-padding-perpendicular: 2px;--os-padding-axis: 2px;--os-track-border-radius: 10px;--os-handle-interactive-area-offset: 4px;--os-handle-border-radius: 10px}.os-theme-dark{--os-handle-bg: rgba(0, 0, 0, .44);--os-handle-bg-hover: rgba(0, 0, 0, .55);--os-handle-bg-active: rgba(0, 0, 0, .66)}.os-theme-light{--os-handle-bg: rgba(255, 255, 255, .44);--os-handle-bg-hover: rgba(255, 255, 255, .55);--os-handle-bg-active: rgba(255, 255, 255, .66)}.progress-pie{width:60px;height:60px;border-radius:50%;background:#eee;background-image:linear-gradient(to right,transparent 50%,#4CC9D8 0);position:relative;display:inline-block}.progress-pie:before{content:"";display:block;margin-left:50%;height:100%;border-radius:0 100% 100% 0/50%;background-color:inherit;transform-origin:left}.progress-pie:after{content:attr(data-value);position:absolute;width:70%;height:70%;margin:auto;border-radius:50%;background-color:#fff;left:0;right:0;top:0;bottom:0;text-align:center;font:900 20px/41px Tahoma}.progress-pie[data-value="0"]:before{transform:rotate(0)}.progress-pie[data-value="1"]:before{transform:rotate(.01turn)}.progress-pie[data-value="2"]:before{transform:rotate(.02turn)}.progress-pie[data-value="3"]:before{transform:rotate(.03turn)}.progress-pie[data-value="4"]:before{transform:rotate(.04turn)}.progress-pie[data-value="5"]:before{transform:rotate(.05turn)}.progress-pie[data-value="6"]:before{transform:rotate(.06turn)}.progress-pie[data-value="7"]:before{transform:rotate(.07turn)}.progress-pie[data-value="8"]:before{transform:rotate(.08turn)}.progress-pie[data-value="9"]:before{transform:rotate(.09turn)}.progress-pie[data-value="10"]:before{transform:rotate(.1turn)}.progress-pie[data-value="11"]:before{transform:rotate(.11turn)}.progress-pie[data-value="12"]:before{transform:rotate(.12turn)}.progress-pie[data-value="13"]:before{transform:rotate(.13turn)}.progress-pie[data-value="14"]:before{transform:rotate(.14turn)}.progress-pie[data-value="15"]:before{transform:rotate(.15turn)}.progress-pie[data-value="16"]:before{transform:rotate(.16turn)}.progress-pie[data-value="17"]:before{transform:rotate(.17turn)}.progress-pie[data-value="18"]:before{transform:rotate(.18turn)}.progress-pie[data-value="19"]:before{transform:rotate(.19turn)}.progress-pie[data-value="20"]:before{transform:rotate(.2turn)}.progress-pie[data-value="21"]:before{transform:rotate(.21turn)}.progress-pie[data-value="22"]:before{transform:rotate(.22turn)}.progress-pie[data-value="23"]:before{transform:rotate(.23turn)}.progress-pie[data-value="24"]:before{transform:rotate(.24turn)}.progress-pie[data-value="25"]:before{transform:rotate(.25turn)}.progress-pie[data-value="26"]:before{transform:rotate(.26turn)}.progress-pie[data-value="27"]:before{transform:rotate(.27turn)}.progress-pie[data-value="28"]:before{transform:rotate(.28turn)}.progress-pie[data-value="29"]:before{transform:rotate(.29turn)}.progress-pie[data-value="30"]:before{transform:rotate(.3turn)}.progress-pie[data-value="31"]:before{transform:rotate(.31turn)}.progress-pie[data-value="32"]:before{transform:rotate(.32turn)}.progress-pie[data-value="33"]:before{transform:rotate(.33turn)}.progress-pie[data-value="34"]:before{transform:rotate(.34turn)}.progress-pie[data-value="35"]:before{transform:rotate(.35turn)}.progress-pie[data-value="36"]:before{transform:rotate(.36turn)}.progress-pie[data-value="37"]:before{transform:rotate(.37turn)}.progress-pie[data-value="38"]:before{transform:rotate(.38turn)}.progress-pie[data-value="39"]:before{transform:rotate(.39turn)}.progress-pie[data-value="40"]:before{transform:rotate(.4turn)}.progress-pie[data-value="41"]:before{transform:rotate(.41turn)}.progress-pie[data-value="42"]:before{transform:rotate(.42turn)}.progress-pie[data-value="43"]:before{transform:rotate(.43turn)}.progress-pie[data-value="44"]:before{transform:rotate(.44turn)}.progress-pie[data-value="45"]:before{transform:rotate(.45turn)}.progress-pie[data-value="46"]:before{transform:rotate(.46turn)}.progress-pie[data-value="47"]:before{transform:rotate(.47turn)}.progress-pie[data-value="48"]:before{transform:rotate(.48turn)}.progress-pie[data-value="49"]:before{transform:rotate(.49turn)}.progress-pie[data-value="50"]:before{transform:rotate(.5turn)}.progress-pie[data-value="51"]:before{background-color:#4cc9d8;transform:rotate(.01turn)}.progress-pie[data-value="52"]:before{background-color:#4cc9d8;transform:rotate(.02turn)}.progress-pie[data-value="53"]:before{background-color:#4cc9d8;transform:rotate(.03turn)}.progress-pie[data-value="54"]:before{background-color:#4cc9d8;transform:rotate(.04turn)}.progress-pie[data-value="55"]:before{background-color:#4cc9d8;transform:rotate(.05turn)}.progress-pie[data-value="56"]:before{background-color:#4cc9d8;transform:rotate(.06turn)}.progress-pie[data-value="57"]:before{background-color:#4cc9d8;transform:rotate(.07turn)}.progress-pie[data-value="58"]:before{background-color:#4cc9d8;transform:rotate(.08turn)}.progress-pie[data-value="59"]:before{background-color:#4cc9d8;transform:rotate(.09turn)}.progress-pie[data-value="60"]:before{background-color:#4cc9d8;transform:rotate(.1turn)}.progress-pie[data-value="61"]:before{background-color:#4cc9d8;transform:rotate(.11turn)}.progress-pie[data-value="62"]:before{background-color:#4cc9d8;transform:rotate(.12turn)}.progress-pie[data-value="63"]:before{background-color:#4cc9d8;transform:rotate(.13turn)}.progress-pie[data-value="64"]:before{background-color:#4cc9d8;transform:rotate(.14turn)}.progress-pie[data-value="65"]:before{background-color:#4cc9d8;transform:rotate(.15turn)}.progress-pie[data-value="66"]:before{background-color:#4cc9d8;transform:rotate(.16turn)}.progress-pie[data-value="67"]:before{background-color:#4cc9d8;transform:rotate(.17turn)}.progress-pie[data-value="68"]:before{background-color:#4cc9d8;transform:rotate(.18turn)}.progress-pie[data-value="69"]:before{background-color:#4cc9d8;transform:rotate(.19turn)}.progress-pie[data-value="70"]:before{background-color:#4cc9d8;transform:rotate(.2turn)}.progress-pie[data-value="71"]:before{background-color:#4cc9d8;transform:rotate(.21turn)}.progress-pie[data-value="72"]:before{background-color:#4cc9d8;transform:rotate(.22turn)}.progress-pie[data-value="73"]:before{background-color:#4cc9d8;transform:rotate(.23turn)}.progress-pie[data-value="74"]:before{background-color:#4cc9d8;transform:rotate(.24turn)}.progress-pie[data-value="75"]:before{background-color:#4cc9d8;transform:rotate(.25turn)}.progress-pie[data-value="76"]:before{background-color:#4cc9d8;transform:rotate(.26turn)}.progress-pie[data-value="77"]:before{background-color:#4cc9d8;transform:rotate(.27turn)}.progress-pie[data-value="78"]:before{background-color:#4cc9d8;transform:rotate(.28turn)}.progress-pie[data-value="79"]:before{background-color:#4cc9d8;transform:rotate(.29turn)}.progress-pie[data-value="80"]:before{background-color:#4cc9d8;transform:rotate(.3turn)}.progress-pie[data-value="81"]:before{background-color:#4cc9d8;transform:rotate(.31turn)}.progress-pie[data-value="82"]:before{background-color:#4cc9d8;transform:rotate(.32turn)}.progress-pie[data-value="83"]:before{background-color:#4cc9d8;transform:rotate(.33turn)}.progress-pie[data-value="84"]:before{background-color:#4cc9d8;transform:rotate(.34turn)}.progress-pie[data-value="85"]:before{background-color:#4cc9d8;transform:rotate(.35turn)}.progress-pie[data-value="86"]:before{background-color:#4cc9d8;transform:rotate(.36turn)}.progress-pie[data-value="87"]:before{background-color:#4cc9d8;transform:rotate(.37turn)}.progress-pie[data-value="88"]:before{background-color:#4cc9d8;transform:rotate(.38turn)}.progress-pie[data-value="89"]:before{background-color:#4cc9d8;transform:rotate(.39turn)}.progress-pie[data-value="90"]:before{background-color:#4cc9d8;transform:rotate(.4turn)}.progress-pie[data-value="91"]:before{background-color:#4cc9d8;transform:rotate(.41turn)}.progress-pie[data-value="92"]:before{background-color:#4cc9d8;transform:rotate(.42turn)}.progress-pie[data-value="93"]:before{background-color:#4cc9d8;transform:rotate(.43turn)}.progress-pie[data-value="94"]:before{background-color:#4cc9d8;transform:rotate(.44turn)}.progress-pie[data-value="95"]:before{background-color:#4cc9d8;transform:rotate(.45turn)}.progress-pie[data-value="96"]:before{background-color:#4cc9d8;transform:rotate(.46turn)}.progress-pie[data-value="97"]:before{background-color:#4cc9d8;transform:rotate(.47turn)}.progress-pie[data-value="98"]:before{background-color:#4cc9d8;transform:rotate(.48turn)}.progress-pie[data-value="99"]:before{background-color:#4cc9d8;transform:rotate(.49turn)}.progress-pie[data-value="100"]:before{background-color:#4cc9d8;transform:rotate(.5turn)}@keyframes pulse{0%{opacity:.8}to{opacity:.2}}@keyframes shrink{0%{width:100%}to{width:0%}}.day-planner{position:relative}.day-planner .status-bar-item-segment:hover{cursor:pointer}.status-bar-item.plugin-obsidian-day-planner{display:flex;gap:var(--size-2-2);padding-block:0}.day-planner-progress-bar{overflow:hidden;display:flex;align-items:stretch;align-self:stretch;min-width:100px;background-color:var(--text-faint);border-radius:var(--radius-s)}.day-planner-progress-value{background-color:var(--color-accent);height:100%}.day-planner-progress-value.green,.day-planner .progress-pie.green:before{background-color:#4caf50}.day-planner .progress-pie.green{background-image:linear-gradient(to right,transparent 50%,#4caf50 0)}.day-planner-status-bar-text{float:left;margin-right:10px}.day-planner-status-card{position:absolute;top:-140px;display:none;width:300px;padding:8px;background-color:var(--background-secondary-alt);border-radius:4px}.day-planner-status-card .arrow-down{position:absolute;width:20px;border-top:20px solid var(--background-secondary-alt);border-right:20px solid transparent;border-left:20px solid transparent}.progress-pie.day-planner{width:20px;height:20px}.progress-pie.day-planner:after{width:80%;height:80%}.progress-pie.day-planner:after{font-size:8px;font-weight:900;line-height:13px;color:transparent;background-color:transparent}[data-type=planner-timeline] .view-content,[data-type=planner-weekly] .view-content{--scrollbar-width: 12px;--timeline-flex-basis: 240px;--shadow-color: #00000010;--shadow-right: 2px 0px 2px var(--shadow-color);--shadow-bottom: 0px 2px 2px var(--shadow-color);--shadow-stationary: 0px .5px 1px .5px rgba(0, 0, 0, .1);--shadow-drag: 0px 2px 10px rgba(0, 0, 0, .1);--shadow-border-accent: 0 0 0 2px var(--color-accent);display:flex;flex-direction:column;padding:0}body{--floating-controls-shadow: 0px 2px 10px rgba(0, 0, 0, .07)}[data-type=planner-weekly] .view-header{display:grid;grid-template-columns:repeat(3,1fr)}[data-type=planner-weekly] .view-content{display:grid;grid-template-rows:auto auto 1fr;grid-template-columns:auto 1fr auto auto;grid-template-areas:"corner dates settings" "corner multiday settings" "ruler timelines settings"}[data-type=planner-weekly] .view-actions{gap:var(--size-4-2);justify-self:end}[data-type=planner-weekly] .view-header-title-container{grid-column:2}[data-type=planner-weekly] .view-header-title{font-size:var(--font-ui-medium);color:var(--text-muted)}.os-scrollbar{box-sizing:border-box;--os-size: var(--scrollbar-width);--os-padding-perpendicular: 2px;--os-padding-axis: 2px;--os-track-border-radius: 10px;--os-handle-interactive-area-offset: 4px;--os-handle-border-radius: 10px;--os-handle-bg: var(--scrollbar-thumb-bg);--os-handle-bg-hover: var(--scrollbar-active-thumb-bg);--os-handle-bg-active: var(--scrollbar-active-thumb-bg)}.absolute-stretch-x{position:absolute;right:0;left:0}.day-planner-release-notes-modal .modal-content{height:100%;overflow:auto}.day-planner-modal-buttons{display:flex;justify-content:flex-end;gap:var(--size-4-2)}.overlayscrollbars-svelte{width:100%;height:100%}.day-planner-release-notes-container{max-width:var(--file-line-width);margin-left:auto;margin-right:auto}.undo-timeout-bar{position:absolute;bottom:0;left:0;width:100%;height:3px;background-color:var(--color-accent)}.notice:has(.undo-timeout-bar){position:relative;overflow:hidden}.planner-sticky-block-content{position:sticky;top:0;overflow:hidden;max-height:100%}
