# 时间线可视化插件配置指南

## 🎯 推荐插件

### 1. Timeline 插件 (⭐⭐⭐⭐⭐)

**安装方法：**
1. 在 Obsidian 设置 → 社区插件中搜索 "Timeline"
2. 安装并启用插件

**基本用法：**
```timeline
title: 我的学习时间线
date-format: YYYY-MM-DD HH:mm

2025-07-28 10:45: 开始整理单词到anki
2025-07-28 11:03: 完成单词整理，浏览reddit
2025-07-28 16:31: 开始anki背单词
2025-07-28 17:21: 完成单词学习，使用youglish
```

### 2. Dataview + 自定义时间线 (⭐⭐⭐⭐)

**优点：**
- 直接利用现有的事件日志数据
- 高度可定制
- 与现有系统完美集成

## 📊 为你的系统定制时间线视图

### 方案一：每日时间线视图

创建一个专门的时间线视图文件：
