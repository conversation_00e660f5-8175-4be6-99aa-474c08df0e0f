---
标题: Dataview调试测试
创建日期: 2025-07-31
tags:
  - 调试
  - dataview
---

# Dataview调试测试

## 测试基本查询

```dataviewjs
// 测试基本功能
dv.paragraph("Dataview基本功能测试");

// 测试获取当前文件
const currentFile = dv.current();
dv.paragraph(`当前文件名: ${currentFile.file.name}`);

// 测试获取页面
const allPages = dv.pages('"🎓 study/📌 学习项目/每日记录"').where(p => p.tags && p.tags.includes("每日记录"));
dv.paragraph(`找到的每日记录页面数量: ${allPages.length}`);

// 测试数组转换
const pagesArray = allPages.array();
dv.paragraph(`转换为数组后的长度: ${pagesArray.length}`);

// 如果有页面，显示第一个页面的信息
if (pagesArray.length > 0) {
  const firstPage = pagesArray[0];
  dv.paragraph(`第一个页面: ${firstPage.file.name}`);
  dv.paragraph(`项目字段: ${firstPage.项目 || '未设置'}`);
  dv.paragraph(`日期字段: ${firstPage.日期 || '未设置'}`);
}

// 测试过滤功能
const filteredPages = allPages.where(p => p.项目 === "阮一峰《C语言教程》").array();
dv.paragraph(`过滤后的页面数量: ${filteredPages.length}`);
```

## 测试项目查询

```dataviewjs
// 测试项目文件查询
const projectName = "阮一峰《C语言教程》";
dv.paragraph(`测试项目: ${projectName}`);

// 获取每日记录
const dailyRecords = dv.pages('"🎓 study/📌 学习项目/每日记录"').where(p => p.tags && p.tags.includes("每日记录") && p.项目 === projectName).array();
dv.paragraph(`找到的每日记录数量: ${dailyRecords.length}`);

if (dailyRecords.length > 0) {
  dv.paragraph("每日记录详情:");
  dailyRecords.forEach((record, index) => {
    dv.paragraph(`${index + 1}. ${record.file.name} - 项目: ${record.项目} - 日期: ${record.日期}`);
  });
  
  // 测试数据计算
  const totalTime = dailyRecords.map(p => p.学习时长 || 0).reduce((a, b) => a + b, 0);
  dv.paragraph(`总学习时长: ${totalTime} 分钟`);
  
  const avgEfficiency = dailyRecords.length > 0 ? 
    Math.round(dailyRecords.map(p => p.效率评分 || 5).reduce((a, b) => a + b, 0) / dailyRecords.length * 10) / 10 : 0;
  dv.paragraph(`平均效率: ${avgEfficiency}/10`);
}
```
