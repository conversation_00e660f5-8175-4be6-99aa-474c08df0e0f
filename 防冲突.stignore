// Syncthing 忽略规则 for Obsidian
// ------------------------------------
// 这是一个注释，以 // 开头的行将被忽略

// 1. 忽略工作区文件 (强烈推荐)
// 这两个文件记录了你上次打开了哪些笔记、窗口大小和布局等。
// 在不同设备上（比如电脑和手机）保留独立的布局体验更好。
.obsidian/workspace.json
.obsidian/workspaces.json

// 2. 忽略缓存文件 (强烈推荐)
// Obsidian 的缓存文件，完全没有同步的必要。
.obsidian/cache

// 3. 忽略移动端特定的配置文件 (推荐)
// 如果同步，可能会导致桌面端的配置被覆盖。
.obsidian/mobile.json

// 4. (可选) 忽略特定插件的配置或缓存
// 如果你发现某个插件总是在制造同步冲突，可以在这里忽略它。
// 把下面的 "some-plugin-folder" 换成那个插件的真实文件夹名。
// .obsidian/plugins/some-plugin-folder/

// 5. (可选) 忽略回收站内容
// 如果你不希望一个设备上删除的文件在另一个设备的回收站里出现。
// .trash/