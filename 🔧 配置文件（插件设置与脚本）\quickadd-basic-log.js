module.exports = async (params) => {
    const { quickAddApi: { inputPrompt }, app } = params;
    
    // 获取当前时间
    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 5); // HH:mm 格式

    // 获取今天的日记文件路径（使用本地时区）
    const today = now.getFullYear() + '-' +
                 String(now.getMonth() + 1).padStart(2, '0') + '-' +
                 String(now.getDate()).padStart(2, '0');
    const dailyNotePath = `🎓 study/📆 学习日志/日记/${today}.md`;
    
    try {
        // 检查今天的日记是否存在
        const dailyFile = app.vault.getAbstractFileByPath(dailyNotePath);
        if (!dailyFile) {
            new Notice(`今天的日记文件不存在: ${dailyNotePath}`);
            return;
        }
        
        // 提示用户输入开始时间
        const startTime = await inputPrompt("开始时间 (HH:mm 格式，留空则需要手动填写):");
        
        // 计算时长（如果提供了开始时间）
        let timeRange = "";
        let duration = "";
        
        if (startTime && startTime.match(/^\d{2}:\d{2}$/)) {
            const [startHour, startMin] = startTime.split(':').map(Number);
            const [endHour, endMin] = currentTime.split(':').map(Number);
            
            const startMinutes = startHour * 60 + startMin;
            const endMinutes = endHour * 60 + endMin;
            let diffMinutes = endMinutes - startMinutes;
            
            // 处理跨天情况
            if (diffMinutes < 0) {
                diffMinutes += 24 * 60;
            }
            
            timeRange = `${startTime} - ${currentTime}`;
            duration = `${diffMinutes}min`;
        } else {
            timeRange = `<开始时间> - ${currentTime}`;
            duration = "?min";
        }
        
        // 生成事件日志条目
        const eventLogEntry = `
### 📌 ${currentTime} 事件日志
- **时间段：** \`${timeRange}\`  ${duration}
- **做了什么：** 
\t- 
- **当时想法与感悟：**
\t- 

`;
        
        // 读取当前日记内容
        const content = await app.vault.read(dailyFile);
        const lines = content.split('\n');
        
        // 智能插入逻辑：按时间顺序插入（从早到晚）
        let insertIndex = -1;
        let eventLogStartIndex = -1;

        // 找到事件日志部分的开始位置
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes('## 📌 事件日志')) {
                eventLogStartIndex = i;
                break;
            }
        }

        if (eventLogStartIndex === -1) {
            new Notice("未找到事件日志部分，请检查日记模板");
            return;
        }

        // 解析当前事件日志的时间，找到合适的插入位置
        const newEventTime = currentTime; // 新事件的时间
        const newEventMinutes = parseInt(newEventTime.split(':')[0]) * 60 + parseInt(newEventTime.split(':')[1]);

        insertIndex = eventLogStartIndex + 1; // 默认插入到事件日志部分开头

        // 遍历现有的事件日志条目，找到按时间顺序的插入位置
        for (let i = eventLogStartIndex + 1; i < lines.length; i++) {
            const line = lines[i];

            // 如果遇到下一个主要部分，停止搜索
            if (line.startsWith('## ') && !line.includes('📌 事件日志')) {
                break;
            }

            // 查找事件日志条目的时间标题
            const timeMatch = line.match(/### 📌 (\d{2}:\d{2}) 事件日志/);
            if (timeMatch) {
                const existingTime = timeMatch[1];
                const existingMinutes = parseInt(existingTime.split(':')[0]) * 60 + parseInt(existingTime.split(':')[1]);

                // 如果新事件时间早于现有事件时间，在此处插入
                if (newEventMinutes < existingMinutes) {
                    insertIndex = i;
                    break;
                }

                // 如果新事件时间晚于现有事件时间，继续寻找
                insertIndex = i;

                // 跳过当前事件日志条目的内容
                for (let j = i + 1; j < lines.length; j++) {
                    if (lines[j].startsWith('### ') || lines[j].startsWith('## ')) {
                        insertIndex = j;
                        break;
                    }
                    if (j === lines.length - 1) {
                        insertIndex = lines.length;
                        break;
                    }
                }
                i = insertIndex - 1; // 调整循环索引
            }
        }

        // 在找到的位置插入新条目
        lines.splice(insertIndex, 0, eventLogEntry);
        const newContent = lines.join('\n');
        
        // 写入文件
        await app.vault.modify(dailyFile, newContent);
        
        new Notice(`事件日志已添加到 ${today}.md`);
        
        // 打开今天的日记文件
        const leaf = app.workspace.getLeaf(false);
        await leaf.openFile(dailyFile);
        
    } catch (error) {
        console.error('添加事件日志时出错:', error);
        new Notice(`添加事件日志失败: ${error.message}`);
    }
};
