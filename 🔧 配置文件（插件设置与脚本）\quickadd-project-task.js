module.exports = async (params) => {
    const { quickAddApi: { suggester, inputPrompt }, app } = params;
    
    try {
        // 获取所有项目文件
        const projectFolder = app.vault.getAbstractFileByPath("🎓 study/📌 学习项目");
        if (!projectFolder) {
            new Notice("项目文件夹不存在");
            return;
        }
        
        const projectFiles = app.vault.getMarkdownFiles()
            .filter(file => file.path.startsWith("🎓 study/📌 学习项目/") && 
                           file.name !== "项目管理看板.md");
        
        if (projectFiles.length === 0) {
            new Notice("没有找到项目文件");
            return;
        }
        
        // 让用户选择项目
        const projectNames = projectFiles.map(file => file.basename);
        const selectedProject = await suggester(projectNames, projectFiles);
        
        if (!selectedProject) {
            return; // 用户取消选择
        }
        
        // 读取选中的项目文件
        const projectContent = await app.vault.read(selectedProject);
        
        // 提取项目中的未完成任务
        const lines = projectContent.split('\n');
        const tasks = [];
        
        for (let line of lines) {
            if (line.trim().startsWith('- [ ]') && line.includes('#task')) {
                // 清理任务文本，移除多余的标签和格式
                let taskText = line.trim()
                    .replace(/^- \[ \] /, '')
                    .replace(/#task\s*/, '')
                    .replace(/#项目\s*/, '')
                    .replace(/#学习\s*/, '')
                    .trim();
                
                tasks.push({
                    original: line.trim(),
                    display: taskText,
                    text: taskText
                });
            }
        }
        
        if (tasks.length === 0) {
            new Notice(`项目 "${selectedProject.basename}" 中没有找到未完成的任务`);
            return;
        }
        
        // 让用户选择要添加的任务
        const taskDisplays = tasks.map(task => task.display);
        const selectedTaskIndex = await suggester(taskDisplays, tasks.map((_, index) => index));
        
        if (selectedTaskIndex === undefined) {
            return; // 用户取消选择
        }
        
        const selectedTask = tasks[selectedTaskIndex];
        
        // 获取今天的日记文件路径
        const today = new Date().toISOString().slice(0, 10);
        const dailyNotePath = `🎓 study/📆 学习日志/日记/${today}.md`;
        
        // 检查今天的日记是否存在
        const dailyFile = app.vault.getAbstractFileByPath(dailyNotePath);
        if (!dailyFile) {
            new Notice(`今天的日记文件不存在: ${dailyNotePath}`);
            return;
        }
        
        // 询问优先级
        const priorities = ["⏫ 高优先级", "🔼 中优先级", "📌 普通", "🔽 低优先级"];
        const selectedPriority = await suggester(priorities, priorities);
        
        // 构建任务文本
        const taskText = `- [ ] #task ${selectedTask.text} 📅 ${today} ${selectedPriority || "📌"} 📂[[${selectedProject.basename}]]`;
        
        // 读取今天的日记内容
        const dailyContent = await app.vault.read(dailyFile);
        const dailyLines = dailyContent.split('\n');
        
        // 找到手动添加任务的部分
        let insertIndex = -1;
        for (let i = 0; i < dailyLines.length; i++) {
            if (dailyLines[i].includes('### 🎯 手动添加的任务')) {
                // 找到下一个空行或下一个标题
                for (let j = i + 1; j < dailyLines.length; j++) {
                    if (dailyLines[j].trim() === '' || dailyLines[j].startsWith('#')) {
                        insertIndex = j;
                        break;
                    }
                }
                break;
            }
        }
        
        if (insertIndex === -1) {
            // 如果没找到特定位置，就在任务部分的末尾添加
            for (let i = 0; i < dailyLines.length; i++) {
                if (dailyLines[i].includes('## ✅ 今日学习任务')) {
                    // 找到任务部分后的第一个其他部分
                    for (let j = i + 1; j < dailyLines.length; j++) {
                        if (dailyLines[j].startsWith('##') && !dailyLines[j].includes('今日学习任务')) {
                            insertIndex = j;
                            break;
                        }
                    }
                    break;
                }
            }
        }
        
        if (insertIndex === -1) {
            insertIndex = dailyLines.length; // 添加到文件末尾
        }
        
        // 插入任务
        dailyLines.splice(insertIndex, 0, taskText);
        const newContent = dailyLines.join('\n');
        
        // 写入文件
        await app.vault.modify(dailyFile, newContent);
        
        new Notice(`已将任务"${selectedTask.text}"添加到今日计划`);
        
        // 打开今天的日记文件
        const leaf = app.workspace.getLeaf(false);
        await leaf.openFile(dailyFile);
        
    } catch (error) {
        console.error('添加项目任务时出错:', error);
        new Notice(`添加项目任务失败: ${error.message}`);
    }
};
