---
标题: 每日进度跟踪系统 - QuickAdd配置说明
创建日期: 2025-07-31
更新日期: 2025-07-31
tags:
  - 配置说明
  - QuickAdd
  - 每日进度跟踪
  - 插件设置
cssclasses:
  - config-page
---

# 🔧 每日进度跟踪系统 - QuickAdd配置说明

> **系统版本**: 1.0  
> **配置日期**: 2025-07-31  
> **适用范围**: Obsidian 每日进度跟踪系统

## 📋 配置概述

本文档专门说明每日进度跟踪系统的QuickAdd配置步骤，包括两个核心功能：
- **每日学习记录**: 快速记录每天的学习进度
- **进度仪表板**: 生成项目进度分析报告

## 🚀 前置要求

### 1. 插件安装
确保已安装并启用以下插件：
- **QuickAdd**: 用于快速操作
- **Dataview**: 用于数据查询和展示
- **Templater**: 用于模板功能（可选）

### 2. 文件结构
确保以下文件夹结构存在：
```
🎓 study/📌 学习项目/
├── 每日记录/
│   └── {项目名称}/
└── 进度仪表板/
```

## ⚙️ 详细配置步骤

### 配置一：每日学习记录

#### 1. 添加QuickAdd命令
1. **打开QuickAdd设置**
   - 设置 → 第三方插件 → QuickAdd → 设置

2. **添加Macro命令**
   - 点击 "Add Choice"
   - 选择 "Macro"
   - 命令名称: `📅 每日学习记录`

3. **配置Macro设置**
   - 点击 "Configure" 配置宏
   - 添加 "User Script" 步骤
   - 脚本路径: `🔧 配置文件（插件设置与脚本）/quickadd-daily-progress.js`
   - 保存配置

4. **设置快捷键**
   - 设置 → 快捷键
   - 搜索 "QuickAdd: 📅 每日学习记录"
   - 建议快捷键: `Ctrl+Shift+D`

#### 2. 脚本功能说明
- **项目选择**: 自动列出所有学习项目供选择
- **数据收集**: 收集学习时长、内容、效率评分等
- **智能路径**: 自动创建 `每日记录/{项目名称}/{日期}.md`
- **重复检测**: 检测今日是否已有记录，支持更新
- **自动打开**: 创建后自动打开记录文件

### 配置二：进度仪表板生成

#### 1. 添加QuickAdd命令
1. **添加Macro命令**
   - 命令名称: `📊 生成进度仪表板`
   - 类型: Macro

2. **配置Macro设置**
   - 脚本路径: `🔧 配置文件（插件设置与脚本）/quickadd-progress-dashboard.js`

3. **设置快捷键**
   - 建议快捷键: `Ctrl+Shift+P`

#### 2. 脚本功能说明
- **项目选择**: 选择要分析的项目
- **数据统计**: 自动统计学习数据
- **趋势分析**: 生成学习趋势图表
- **智能建议**: 基于数据提供学习建议
- **自动更新**: 支持更新现有仪表板

## 🎯 使用流程

### 每日记录流程
1. **触发命令**: 按 `Ctrl+Shift+D` 或通过命令面板
2. **选择项目**: 从项目列表中选择要记录的项目
3. **填写数据**: 按提示填写学习时长、内容等信息
4. **自动创建**: 系统自动创建结构化的每日记录
5. **继续编辑**: 在打开的文件中补充详细内容

### 仪表板生成流程
1. **触发命令**: 按 `Ctrl+Shift+P` 或通过命令面板
2. **选择项目**: 选择要分析的项目
3. **自动生成**: 系统自动分析数据并生成仪表板
4. **查看分析**: 在打开的仪表板中查看详细分析

## 📊 数据字段说明

### 每日记录数据结构
```yaml
---
日期: YYYY-MM-DD
项目: "项目名称"
学习时长: 数值（分钟）
完成度: 数值（0-100%）
效率评分: 数值（1-10）
难度评分: 数值（1-10）
tags:
  - 每日记录
  - 项目进度
  - "项目名称"
---
```

### 关键字段含义
- **学习时长**: 实际有效学习时间（分钟）
- **完成度**: 当天学习目标完成百分比
- **效率评分**: 主观学习效率评价（1-10分）
- **难度评分**: 学习内容难度评价（1-10分）

## 💡 使用技巧

### 数据填写建议
1. **及时记录**: 建议学习结束后立即记录
2. **真实数据**: 确保时长和评分的准确性
3. **详细描述**: 在学习内容中记录具体内容
4. **问题记录**: 及时记录遇到的困难和解决方案

### 分析查看建议
1. **定期生成**: 建议每周生成一次仪表板
2. **趋势关注**: 重点关注学习效率和时长趋势
3. **建议采纳**: 参考系统提供的智能建议
4. **策略调整**: 根据分析结果调整学习策略

## 🔧 自定义配置

### 修改脚本参数
如需自定义功能，可编辑脚本文件：

#### quickadd-daily-progress.js 可修改项
- 默认学习时长
- 数据收集字段
- 文件路径格式
- 提示信息内容

#### quickadd-progress-dashboard.js 可修改项
- 统计时间范围
- 图表显示格式
- 建议生成逻辑
- 仪表板模板

### 路径自定义
可在脚本中修改以下路径：
```javascript
// 每日记录路径
const dailyRecordPath = `🎓 study/📌 学习项目/每日记录/${projectName}/${dateStr}.md`;

// 仪表板路径
const dashboardPath = `🎓 study/📌 学习项目/进度仪表板/${projectName}-进度仪表板.md`;
```

## 🔍 故障排除

### 常见问题

#### 1. 脚本执行失败
**可能原因**:
- 脚本文件路径错误
- 权限不足
- 插件版本不兼容

**解决方案**:
- 检查脚本文件是否存在
- 确认QuickAdd插件已正确启用
- 查看控制台错误信息

#### 2. 数据不显示
**可能原因**:
- Dataview插件未启用
- 文件标签格式错误
- 文件路径不匹配

**解决方案**:
- 启用Dataview插件
- 检查每日记录的标签格式
- 确认文件夹结构正确

#### 3. 项目列表为空
**可能原因**:
- 项目文件路径不正确
- 项目文件不存在

**解决方案**:
- 确认项目文件在 `🎓 study/📌 学习项目/` 目录下
- 检查项目文件是否为Markdown格式

### 调试方法
1. **控制台检查**: 按F12查看控制台错误信息
2. **分步测试**: 逐步执行脚本功能
3. **文件检查**: 确认相关文件和文件夹存在
4. **插件状态**: 检查所有必需插件是否正常工作

## 📈 系统优化建议

### 性能优化
1. **定期清理**: 定期归档旧的每日记录
2. **文件管理**: 保持合理的文件夹结构
3. **数据量控制**: 避免单个项目记录过多

### 功能扩展
1. **自动化**: 可结合其他插件实现更多自动化功能
2. **数据导出**: 可添加数据导出功能
3. **图表增强**: 可集成更丰富的图表显示
4. **提醒功能**: 可添加学习提醒功能

---
*配置文档版本: 1.0 | 最后更新: 2025-07-31*
