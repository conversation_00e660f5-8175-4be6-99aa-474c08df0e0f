# Kanban 看板配置说明

## 📋 配置要点

### 1. Frontmatter 字段映射
为了让 Kanban 插件能够自动更新项目文件的 frontmatter，需要确保：

**项目文件的 frontmatter 必须包含：**
```yaml
---
状态: 待办  # 或 进行中、已完成、搁置
---
```

**看板配置中的关键设置：**
```json
{
  "frontmatter-property-trigger": "状态",
  "update-frontmatter": true,
  "lane-frontmatter-map": {
    "待办": "待办",
    "进行中": "进行中", 
    "已完成": "已完成",
    "搁置": "搁置"
  }
}
```

### 2. 看板泳道与状态对应关系

| 看板泳道 | Frontmatter 状态值 | 说明 |
|---------|------------------|------|
| 待办 | `状态: 待办` | 计划要做但还未开始的项目 |
| 进行中 | `状态: 进行中` | 正在进行的项目 |
| 已完成 | `状态: 已完成` | 已经完成的项目 |
| 搁置 | `状态: 搁置` | 暂时停止的项目 |

## 🚀 使用方法

### 拖拽更新状态
1. 在项目管理看板中，直接拖拽项目卡片到不同泳道
2. 插件会自动更新对应项目文件的 `状态` 字段
3. 其他依赖状态的查询和视图会自动更新

### 手动更新状态
如果拖拽不生效，可以手动编辑项目文件的 frontmatter：
```yaml
---
状态: 进行中  # 改为对应的状态值
---
```

## 🔧 故障排除

### 问题1：拖拽后状态不更新
**可能原因：**
- 项目文件缺少 `状态` 字段
- 看板配置中的映射关系不正确
- Kanban 插件版本过旧

**解决方案：**
1. 检查项目文件是否包含 `状态: 待办` 字段
2. 确认看板设置中 `update-frontmatter` 为 `true`
3. 重启 Obsidian 或重新加载插件

### 问题2：新建项目卡片位置错误
**解决方案：**
1. 确保看板设置中指定了正确的新建文件夹：
   ```json
   "new-note-folder": "🎓 study/📌 学习项目"
   ```
2. 设置了正确的模板：
   ```json
   "new-note-template": "🗂 模板库/project.md"
   ```

### 问题3：卡片显示异常
**解决方案：**
1. 检查项目文件名是否包含特殊字符
2. 确认文件路径正确
3. 重新打开看板文件

## 💡 最佳实践

### 1. 项目文件命名规范
- 使用简洁明了的项目名称
- 避免使用特殊字符和空格
- 建议格式：`项目名称.md`

### 2. 状态管理规范
- **待办**：新创建的项目，还未开始执行
- **进行中**：正在积极推进的项目（建议同时进行的项目不超过3个）
- **已完成**：所有任务都已完成的项目
- **搁置**：因为各种原因暂停的项目

### 3. 定期维护
- 每周回顾看板，更新项目状态
- 及时将完成的项目移到"已完成"
- 定期清理不再需要的项目

## 🔄 与其他功能的集成

### 与任务管理的联动
- 项目状态变化会影响任务总览中的显示
- 已完成的项目任务不会在每日计划中显示

### 与数据统计的联动
- 项目进度统计会根据状态字段计算完成率
- 事件日志汇总可以按项目状态筛选

## 📝 模板更新建议

确保项目模板 (`🗂 模板库/project.md`) 包含正确的 frontmatter 格式：

```yaml
---
项目名称: <% tp.file.title %>
创建日期: <% tp.date.now("YYYY-MM-DD") %>
状态: 待办
tags:
  - 项目
  - 学习
cssclasses:
  - project-page
status: Todo
---
```
