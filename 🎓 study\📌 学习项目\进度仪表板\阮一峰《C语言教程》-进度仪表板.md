---
项目: "阮一峰《C语言教程》"
生成日期: 2025-07-31
类型: 进度仪表板
tags:
  - 进度仪表板
  - "阮一峰《C语言教程》"
---

# 📊 阮一峰《C语言教程》 - 进度仪表板

> 生成时间: 2025-07-31 | 数据来源: 每日学习记录

## 🎯 项目概览

```dataviewjs
// 项目基本信息
const projectFile = dv.page("🎓 study/📌 学习项目/阮一峰《C语言教程》.md");
if (projectFile) {
    dv.paragraph(`**项目名称**: ${projectFile.项目名称 || projectFile.file.name}`);
    dv.paragraph(`**创建日期**: ${projectFile.创建日期}`);
    dv.paragraph(`**预计完成**: ${projectFile.预计完成日期}`);
    dv.paragraph(`**当前状态**: ${projectFile.状态}`);
    dv.paragraph(`**优先级**: ${projectFile.优先级}`);
}
```

## 📈 学习数据统计

```dataviewjs
// 获取所有每日记录
const dailyRecords = dv.pages('"🎓 study/📌 学习项目/每日记录/阮一峰《C语言教程》"').where(p => p.tags && p.tags.includes("每日记录")).sort(p => p.日期);

if (dailyRecords.length > 0) {
    // 基础统计
    const totalDays = dailyRecords.length;
    const totalTime = dailyRecords.map(p => p.学习时长 || 0).reduce((a, b) => a + b, 0);
    const avgTime = Math.round(totalTime / totalDays);
    const avgEfficiency = Math.round(dailyRecords.map(p => p.效率评分 || 5).reduce((a, b) => a + b, 0) / totalDays * 10) / 10;
    const avgCompletion = Math.round(dailyRecords.map(p => p.完成度 || 0).reduce((a, b) => a + b, 0) / totalDays);
    
    dv.header(3, "📊 核心数据");
    dv.paragraph(`**总学习天数**: ${totalDays} 天`);
    dv.paragraph(`**累计学习时长**: ${Math.round(totalTime/60*10)/10} 小时 (${totalTime} 分钟)`);
    dv.paragraph(`**日均学习时长**: ${avgTime} 分钟`);
    dv.paragraph(`**平均效率评分**: ${avgEfficiency}/10`);
    dv.paragraph(`**平均完成度**: ${avgCompletion}%`);
    
    // 最近7天趋势
    dv.header(3, "📈 最近7天学习趋势");
    const recent7Days = dailyRecords.slice(-7);
    
    dv.paragraph("**日期 | 时长 | 效率 | 完成度 | 趋势**");
    recent7Days.forEach(record => {
        const date = record.日期;
        const time = record.学习时长 || 0;
        const efficiency = record.效率评分 || 5;
        const completion = record.完成度 || 0;
        const timeBar = "█".repeat(Math.floor(time / 30)) + "░".repeat(Math.max(0, 6 - Math.floor(time / 30)));
        
        dv.paragraph(`${date} | ${time}min | ${efficiency}/10 | ${completion}% | ${timeBar}`);
    });
    
    // 学习效率分析
    dv.header(3, "🎯 效率分析");
    const highEfficiencyDays = dailyRecords.filter(p => (p.效率评分 || 5) >= 8).length;
    const lowEfficiencyDays = dailyRecords.filter(p => (p.效率评分 || 5) <= 4).length;
    const consistentDays = dailyRecords.filter(p => (p.学习时长 || 0) >= 30).length;
    
    dv.paragraph(`**高效率天数** (≥8分): ${highEfficiencyDays} 天 (${Math.round(highEfficiencyDays/totalDays*100)}%)`);
    dv.paragraph(`**低效率天数** (≤4分): ${lowEfficiencyDays} 天 (${Math.round(lowEfficiencyDays/totalDays*100)}%)`);
    dv.paragraph(`**坚持学习天数** (≥30min): ${consistentDays} 天 (${Math.round(consistentDays/totalDays*100)}%)`);
    
} else {
    dv.paragraph("📝 暂无学习记录数据");
}
```

## 📅 最近学习记录

```dataview
TABLE
    学习时长 + "min" as "时长",
    效率评分 + "/10" as "效率",
    完成度 + "%" as "完成度",
    file.link as "详细记录"
FROM "🎓 study/📌 学习项目/每日记录/阮一峰《C语言教程》"
WHERE contains(tags, "每日记录")
SORT 日期 DESC
LIMIT 10
```

## 🎯 学习建议

```dataviewjs
// 基于数据的学习建议
const dailyRecords = dv.pages('"🎓 study/📌 学习项目/每日记录/阮一峰《C语言教程》"').where(p => p.tags && p.tags.includes("每日记录"));

if (dailyRecords.length >= 3) {
    const avgEfficiency = dailyRecords.map(p => p.效率评分 || 5).reduce((a, b) => a + b, 0) / dailyRecords.length;
    const avgTime = dailyRecords.map(p => p.学习时长 || 0).reduce((a, b) => a + b, 0) / dailyRecords.length;
    const recentEfficiency = dailyRecords.slice(-3).map(p => p.效率评分 || 5).reduce((a, b) => a + b, 0) / 3;
    
    dv.header(3, "💡 智能建议");
    
    if (avgEfficiency < 6) {
        dv.paragraph("🔍 **效率提升建议**: 当前平均效率较低，建议调整学习方法或环境");
    } else if (avgEfficiency >= 8) {
        dv.paragraph("🌟 **保持优势**: 学习效率很高，继续保持当前的学习节奏");
    }
    
    if (avgTime < 30) {
        dv.paragraph("⏰ **时间管理建议**: 日均学习时间较短，建议增加学习时长");
    } else if (avgTime > 120) {
        dv.paragraph("🎯 **平衡建议**: 学习时间较长，注意劳逸结合，保持学习质量");
    }
    
    if (recentEfficiency > avgEfficiency) {
        dv.paragraph("📈 **进步趋势**: 最近学习效率有所提升，继续保持！");
    } else if (recentEfficiency < avgEfficiency - 1) {
        dv.paragraph("⚠️ **注意调整**: 最近效率有所下降，建议调整学习策略");
    }
}
```

---
*仪表板更新时间: 2025-07-31*
*数据统计范围: 所有历史记录*