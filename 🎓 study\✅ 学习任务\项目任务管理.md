# 📌 项目任务管理中心

## 🎯 所有项目任务概览

```dataviewjs
// 获取所有项目文件
const projectPages = dv.pages('"🎓 study/📌 学习项目"')
  .where(p => p.file.name !== "项目管理看板");

if (projectPages.length === 0) {
  dv.paragraph("📝 暂无学习项目");
} else {
  let projectData = [];

  for (let page of projectPages) {
    const allTasks = page.file.tasks;
    const uncompletedTasks = allTasks.where(t => !t.completed);
    const completedTasks = allTasks.where(t => t.completed);

    // 获取项目状态
    let projectStatus = "未设置";
    try {
      if (page.状态) projectStatus = page.状态;
      else if (page.status) projectStatus = page.status;
      else {
        // 从文件内容中查找状态
        const content = await dv.io.load(page.file.path);
        const statusMatch = content.match(/状态[：:]\s*(.+)/);
        if (statusMatch) {
          projectStatus = statusMatch[1].trim();
        }
      }
    } catch (error) {
      console.log("获取项目状态失败:", error);
    }

    projectData.push([
      `[[${page.file.name}|${page.file.name}]]`,
      uncompletedTasks.length,
      completedTasks.length,
      projectStatus
    ]);
  }

  // 按状态和项目名排序
  projectData.sort((a, b) => {
    if (a[3] !== b[3]) return a[3].localeCompare(b[3]);
    return a[0].localeCompare(b[0]);
  });

  dv.table(
    ["项目", "未完成任务数", "已完成任务数", "项目状态"],
    projectData
  );
}
```

## 🔥 高优先级任务

```dataviewjs
// 获取所有高优先级任务
const projectPages = dv.pages('"🎓 study/📌 学习项目"')
  .where(p => p.file.name !== "项目管理看板");

let highPriorityTasks = [];

for (let page of projectPages) {
  const tasks = page.file.tasks.where(t =>
    !t.completed && (
      t.text.includes("⏫") ||
      t.text.includes("🔥") ||
      t.text.includes("高优先级")
    )
  );

  for (let task of tasks) {
    highPriorityTasks.push([
      `[[${page.file.name}|${page.file.name}]]`,
      task.text.replace(/^- \[ \] /, '').replace(/#task\/?[^\s]*\s*/g, '').trim(),
      task.due || "无截止日期"
    ]);
  }
}

if (highPriorityTasks.length > 0) {
  dv.table(
    ["项目", "任务", "截止日期"],
    highPriorityTasks
  );
} else {
  dv.paragraph("🎉 当前没有高优先级任务");
}
```

## 📅 有截止日期的任务

```dataviewjs
// 获取所有有截止日期的任务
const projectPages = dv.pages('"🎓 study/📌 学习项目"')
  .where(p => p.file.name !== "项目管理看板");

let dueTasks = [];

for (let page of projectPages) {
  const tasks = page.file.tasks.where(t => !t.completed && t.due);

  for (let task of tasks) {
    dueTasks.push([
      `[[${page.file.name}|${page.file.name}]]`,
      task.text.replace(/^- \[ \] /, '').replace(/#task\/?[^\s]*\s*/g, '').trim(),
      task.due,
      task.due <= new Date().toISOString().slice(0, 10) ? "⚠️ 已过期" : "⏰ 待完成"
    ]);
  }
}

// 按截止日期排序
dueTasks.sort((a, b) => a[2].localeCompare(b[2]));

if (dueTasks.length > 0) {
  dv.table(
    ["项目", "任务", "截止日期", "状态"],
    dueTasks
  );
} else {
  dv.paragraph("📝 当前没有设置截止日期的任务");
}
```

## 📊 项目进度统计

```dataviewjs
// 计算每个项目的完成度和详细统计
const projectPages = dv.pages('"🎓 study/📌 学习项目"')
  .where(p => p.file.name !== "项目管理看板");

if (projectPages.length === 0) {
  dv.paragraph("📝 暂无学习项目");
} else {
  let projectStats = [];
  let totalProjects = 0;
  let totalTasks = 0;
  let totalCompleted = 0;

  for (let page of projectPages) {
    const allTasks = page.file.tasks;
    const completedTasks = allTasks.where(t => t.completed);
    const uncompletedTasks = allTasks.where(t => !t.completed);
    const highPriorityTasks = uncompletedTasks.where(t =>
      t.text.includes("⏫") || t.text.includes("🔥")
    );

    if (allTasks.length > 0) {
      totalProjects++;
      totalTasks += allTasks.length;
      totalCompleted += completedTasks.length;

      const completionRate = Math.round((completedTasks.length / allTasks.length) * 100);

      // 创建更美观的进度条
      const progressBar = "🟩".repeat(Math.floor(completionRate / 10)) +
                         "⬜".repeat(10 - Math.floor(completionRate / 10));

      // 获取项目状态
      let projectStatus = "未设置";
      try {
        if (page.状态) projectStatus = page.状态;
        else if (page.status) projectStatus = page.status;
        else {
          const content = await dv.io.load(page.file.path);
          const statusMatch = content.match(/状态[：:]\s*(.+)/);
          if (statusMatch) {
            projectStatus = statusMatch[1].trim();
          }
        }
      } catch (error) {
        console.log("获取项目状态失败:", error);
      }

      // 状态图标
      const statusIcon = projectStatus === "进行中" ? "🟢" :
                        projectStatus === "待办" ? "🟡" :
                        projectStatus === "已完成" ? "✅" :
                        projectStatus === "暂停" ? "⏸️" : "⚪";

      projectStats.push([
        `[[${page.file.name}|${page.file.name}]]`,
        `${completedTasks.length}/${allTasks.length}`,
        `${completionRate}%`,
        progressBar,
        `${statusIcon} ${projectStatus}`,
        highPriorityTasks.length > 0 ? `🔥 ${highPriorityTasks.length}` : "✅"
      ]);
    }
  }

  // 按完成率排序（未完成的项目在前）
  projectStats.sort((a, b) => {
    const rateA = parseInt(a[2]);
    const rateB = parseInt(b[2]);
    if (rateA === 100 && rateB !== 100) return 1;
    if (rateA !== 100 && rateB === 100) return -1;
    return rateA - rateB;
  });

  if (projectStats.length > 0) {
    dv.table(
      ["项目名称", "完成情况", "完成率", "进度条", "状态", "高优先级"],
      projectStats
    );

    // 显示总体统计
    const overallRate = totalTasks > 0 ? Math.round((totalCompleted / totalTasks) * 100) : 0;
    const overallProgressBar = "🟩".repeat(Math.floor(overallRate / 10)) +
                              "⬜".repeat(10 - Math.floor(overallRate / 10));

    dv.paragraph(`
**📈 总体统计：**
- 📂 项目总数：${totalProjects}
- ✅ 任务总数：${totalTasks}
- 🎯 已完成：${totalCompleted}
- 📊 总体进度：${overallRate}% ${overallProgressBar}
    `);
  } else {
    dv.paragraph("📝 暂无项目任务数据");
  }
}
```

## 🚀 快速操作

### 📌 添加项目任务到今日计划
- 使用 QuickAdd 命令：`📌 添加项目任务到今日`
- 快捷键：`Ctrl+Shift+P`（需要设置）

### 📊 浏览学习项目
- 使用 QuickAdd 命令：`📊 浏览学习项目`
- 快捷键：`Ctrl+Shift+B`（需要设置）

### 📝 创建新项目
- 使用模板：[[🗂 模板库/project-template]]
- 或直接复制现有项目文件进行修改

### 📋 查看项目看板
- [[🎓 study/📌 学习项目/项目管理看板]]

### 🔧 故障排除
- 使用 QuickAdd 命令：`🔧 日期测试`
- 检查日期获取和文件路径是否正确

## 💡 使用建议

1. **优先级标记**：
   - ⏫ 高优先级（紧急重要）
   - 🔼 中优先级（重要不紧急）
   - 📌 普通优先级
   - 🔽 低优先级（不重要不紧急）

2. **截止日期格式**：使用 `📅 YYYY-MM-DD` 格式

3. **项目关联**：在每日任务中使用 `📂[[项目名称]]` 来关联项目

4. **定期回顾**：建议每周回顾项目进度，调整优先级和时间安排

## 📈 实时任务统计

```dataviewjs
// 实时统计所有项目的任务情况
const projectPages = dv.pages('"🎓 study/📌 学习项目"')
  .where(p => p.file.name !== "项目管理看板");

let stats = {
  totalProjects: 0,
  activeProjects: 0,
  totalTasks: 0,
  completedTasks: 0,
  highPriorityTasks: 0,
  overdueTasks: 0,
  todayTasks: 0
};

const today = new Date().toISOString().slice(0, 10);

for (let page of projectPages) {
  stats.totalProjects++;

  // 检查项目是否活跃
  let isActive = false;
  try {
    if (page.状态 === "进行中" || page.status === "进行中") {
      isActive = true;
    } else {
      const content = await dv.io.load(page.file.path);
      if (content.includes("状态: 进行中") || content.includes("状态：进行中")) {
        isActive = true;
      }
    }
  } catch (error) {
    console.log("检查项目状态失败:", error);
  }

  if (isActive) stats.activeProjects++;

  const allTasks = page.file.tasks;
  stats.totalTasks += allTasks.length;

  const completedTasks = allTasks.where(t => t.completed);
  stats.completedTasks += completedTasks.length;

  const uncompletedTasks = allTasks.where(t => !t.completed);

  // 高优先级任务
  const highPriorityTasks = uncompletedTasks.where(t =>
    t.text.includes("⏫") || t.text.includes("🔥")
  );
  stats.highPriorityTasks += highPriorityTasks.length;

  // 过期任务
  const overdueTasks = uncompletedTasks.where(t =>
    t.due && t.due < today
  );
  stats.overdueTasks += overdueTasks.length;

  // 今日任务
  const todayTasks = uncompletedTasks.where(t =>
    t.due && t.due === today
  );
  stats.todayTasks += todayTasks.length;
}

// 计算完成率
const completionRate = stats.totalTasks > 0 ?
  Math.round((stats.completedTasks / stats.totalTasks) * 100) : 0;

// 创建统计卡片
const statsHtml = `
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
  <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 10px; text-align: center;">
    <div style="font-size: 24px; font-weight: bold;">${stats.totalProjects}</div>
    <div style="font-size: 14px; opacity: 0.9;">📂 总项目数</div>
  </div>

  <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 15px; border-radius: 10px; text-align: center;">
    <div style="font-size: 24px; font-weight: bold;">${stats.activeProjects}</div>
    <div style="font-size: 14px; opacity: 0.9;">🟢 活跃项目</div>
  </div>

  <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 15px; border-radius: 10px; text-align: center;">
    <div style="font-size: 24px; font-weight: bold;">${stats.totalTasks}</div>
    <div style="font-size: 14px; opacity: 0.9;">📋 总任务数</div>
  </div>

  <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; padding: 15px; border-radius: 10px; text-align: center;">
    <div style="font-size: 24px; font-weight: bold;">${completionRate}%</div>
    <div style="font-size: 14px; opacity: 0.9;">✅ 完成率</div>
  </div>

  <div style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); color: white; padding: 15px; border-radius: 10px; text-align: center;">
    <div style="font-size: 24px; font-weight: bold;">${stats.highPriorityTasks}</div>
    <div style="font-size: 14px; opacity: 0.9;">🔥 高优先级</div>
  </div>

  <div style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: #333; padding: 15px; border-radius: 10px; text-align: center;">
    <div style="font-size: 24px; font-weight: bold;">${stats.todayTasks}</div>
    <div style="font-size: 14px; opacity: 0.8;">📅 今日任务</div>
  </div>
</div>

${stats.overdueTasks > 0 ? `
<div style="background: #ff6b6b; color: white; padding: 12px; border-radius: 8px; margin: 15px 0; text-align: center;">
  ⚠️ <strong>注意：有 ${stats.overdueTasks} 个任务已过期，请及时处理！</strong>
</div>
` : ''}
`;

const container = dv.container.createDiv();
container.innerHTML = statsHtml;
```
