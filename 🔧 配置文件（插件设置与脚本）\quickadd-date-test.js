module.exports = async (params) => {
    const { app } = params;
    
    try {
        // 测试不同的日期获取方法
        const now = new Date();
        
        // 方法1: toISOString (可能有时区问题)
        const isoDate = now.toISOString().slice(0, 10);
        
        // 方法2: 手动构建 (使用本地时区)
        const localDate = now.getFullYear() + '-' + 
                         String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                         String(now.getDate()).padStart(2, '0');
        
        // 方法3: toLocaleDateString
        const localeDateString = now.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        }).replace(/\//g, '-');
        
        // 显示结果
        const result = `
📅 日期测试结果：

🕐 当前时间: ${now.toString()}
🌍 时区偏移: ${now.getTimezoneOffset()} 分钟

📊 不同方法获取的日期：
1. toISOString(): ${isoDate}
2. 手动构建: ${localDate}
3. toLocaleDateString(): ${localeDateString}

📁 对应的日记文件路径：
1. 🎓 study/📆 学习日志/日记/${isoDate}.md
2. 🎓 study/📆 学习日志/日记/${localDate}.md
3. 🎓 study/📆 学习日志/日记/${localeDateString}.md

✅ 推荐使用方法2（手动构建），因为它使用本地时区。
        `.trim();
        
        // 检查文件是否存在
        const testPaths = [
            `🎓 study/📆 学习日志/日记/${isoDate}.md`,
            `🎓 study/📆 学习日志/日记/${localDate}.md`,
            `🎓 study/📆 学习日志/日记/${localeDateString}.md`
        ];
        
        let existingFiles = [];
        for (const path of testPaths) {
            const file = app.vault.getAbstractFileByPath(path);
            if (file) {
                existingFiles.push(path);
            }
        }
        
        const finalResult = result + `\n\n📋 存在的日记文件：\n${existingFiles.length > 0 ? existingFiles.join('\n') : '无'}`;
        
        // 创建临时文件显示结果
        const tempFile = await app.vault.create('temp-date-test-result.md', finalResult);
        
        // 打开结果文件
        const leaf = app.workspace.getLeaf(false);
        await leaf.openFile(tempFile);
        
        new Notice("日期测试完成，请查看结果文件");
        
        // 5秒后删除临时文件
        setTimeout(async () => {
            try {
                await app.vault.delete(tempFile);
                new Notice("临时测试文件已删除");
            } catch (error) {
                console.error("删除临时文件失败:", error);
            }
        }, 10000);
        
    } catch (error) {
        console.error('日期测试失败:', error);
        new Notice(`日期测试失败: ${error.message}`);
    }
};
