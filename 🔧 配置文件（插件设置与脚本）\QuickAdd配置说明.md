---
status: Todo
---
# QuickAdd 事件日志配置说明

## 📋 配置步骤

### 1. 安装 QuickAdd 插件
- 在 Obsidian 设置 → 社区插件中搜索并安装 "QuickAdd"
- 启用插件

### 2. 配置事件日志命令

#### 方法一：基础事件日志（推荐，最稳定）
1. 打开 QuickAdd 设置
2. 点击 "Add Choice" 添加新选择
3. 设置如下：
   - **Name**: `📌 添加事件日志`
   - **Type**: `Macro`
4. 点击 "Configure" 配置宏
5. 添加 "User Script" 步骤
6. 在脚本路径中输入：`🔧 配置文件（插件设置与脚本）/quickadd-basic-log.js`
7. 保存配置

#### 方法二：项目任务选择器（推荐用于项目管理）
1. 添加另一个选择：
   - **Name**: `📌 添加项目任务到今日`
   - **Type**: `Macro`
2. 配置脚本路径：`🔧 配置文件（插件设置与脚本）/quickadd-project-task-selector.js`

#### 方法三：项目浏览器
1. 添加另一个选择：
   - **Name**: `📊 浏览学习项目`
   - **Type**: `Macro`
2. 配置脚本路径：`🔧 配置文件（插件设置与脚本）/quickadd-project-browser.js`

#### 方法四：快速记录（一行输入）
1. 添加另一个选择：
   - **Name**: `⚡ 快速记录`
   - **Type**: `Macro`
2. 配置脚本路径：`🔧 配置文件（插件设置与脚本）/quickadd-simple-log.js`

#### 方法五：日期测试工具（故障排除用）
1. 添加另一个选择：
   - **Name**: `🔧 日期测试`
   - **Type**: `Macro`
2. 配置脚本路径：`🔧 配置文件（插件设置与脚本）/quickadd-date-test.js`

### 3. 设置快捷键（可选）
1. 在 Obsidian 设置 → 快捷键中搜索 "QuickAdd"
2. 为 "📌 添加事件日志" 设置快捷键，例如 `Ctrl+Shift+L`
3. 为 "📌 添加项目任务到今日" 设置快捷键，例如 `Ctrl+Shift+P`
4. 为 "📊 浏览学习项目" 设置快捷键，例如 `Ctrl+Shift+B`
5. 为 "⚡ 快速记录" 设置快捷键，例如 `Ctrl+Shift+Q`

## 🚀 使用方法

### 基础事件日志模式
1. 按快捷键或通过命令面板执行 "📌 添加事件日志"
2. 输入开始时间（可选，格式：HH:mm）
3. 自动插入到今天的日记中，内容部分留空供手动填写
4. **智能时间排序**：✅ 新功能！事件日志会按时间从早到晚自动排序插入
   - 早上的事件会插入到上方
   - 晚上的事件会插入到下方
   - 保持时间线的逻辑顺序

### 项目任务选择器模式（推荐用于项目管理）
1. 按快捷键或通过命令面板执行 "📌 添加项目任务到今日"
2. 系统会显示所有进行中项目的未完成任务，按优先级排序
3. 选择要添加到今日计划的任务
4. 可以修改任务描述或直接使用原描述
5. 任务会自动添加到今日学习任务列表中，保留优先级标记

### 项目浏览器模式
1. 按快捷键或通过命令面板执行 "📊 浏览学习项目"
2. 查看所有项目的状态、进度和任务数量
3. 项目按状态和未完成任务数量排序
4. 选择项目后会自动打开项目文件并显示项目摘要

### 快速记录模式
1. 按快捷键或执行 "⚡ 快速记录"
2. 输入格式：`开始时间-结束时间 学习内容`
   - 例如：`14:30-15:20 数学习题练习`
   - 或者：`14:30- 数学习题练习`（自动使用当前时间作为结束时间）
3. 自动计算时长并插入日记

## 💡 使用技巧

1. **时间格式**：始终使用 24 小时制的 HH:mm 格式
2. **跨天处理**：脚本会自动处理跨天的时间计算
3. **文件检查**：如果今天的日记不存在，会提示创建
4. **智能时间排序**：✅ 新功能！
   - 事件日志会按时间从早到晚自动排序
   - 新添加的事件会插入到正确的时间位置
   - 例如：11:00 → 11:40 → 15:03（而不是倒序）
   - 保持时间线的逻辑连续性
5. **智能插入位置**：
   - 系统会自动分析现有事件的时间
   - 将新事件插入到时间顺序正确的位置
   - 如果是最早的事件，插入到顶部
   - 如果是最晚的事件，插入到底部
6. **自动定位**：添加后会自动打开今天的日记文件

## 🔧 自定义选项

如果需要修改脚本行为，可以编辑对应的 .js 文件：

- 修改时间格式
- 调整插入位置
- 自定义提示信息
- 添加更多字段

## 🎯 智能时间排序插入说明

### 插入逻辑（✅ 已升级）
脚本现在使用智能时间排序算法：

1. **时间解析**：
   - 自动解析新事件的时间（HH:mm 格式）
   - 将时间转换为分钟数便于比较
   - 支持 24 小时制时间格式

2. **位置计算**：
   - 扫描现有的所有事件日志条目
   - 解析每个条目的时间标题（### 📌 XX:XX 事件日志）
   - 按时间从早到晚的顺序找到正确插入位置

3. **智能插入**：
   - **早于所有现有事件**：插入到事件日志部分的开头
   - **晚于所有现有事件**：插入到事件日志部分的末尾
   - **介于两个事件之间**：插入到正确的时间位置
   - 保持时间线的逻辑顺序：09:00 → 11:30 → 14:20 → 16:45

### 最佳使用方式
- **随时添加**：✅ 无需考虑插入位置，直接执行命令即可
- **自动排序**：系统会自动将事件按时间从早到晚排列
- **时间连续性**：保持完美的时间线顺序，便于回顾和分析
- **快速操作**：无需手动定位，脚本智能处理所有排序逻辑

### 使用示例
假设你已有以下事件：
- 09:00 晨读
- 14:00 数学练习

当你添加 11:30 的英语学习时，系统会自动排序为：
- 09:00 晨读
- 11:30 英语学习 ← 自动插入到正确位置
- 14:00 数学练习

## ❗ 注意事项

1. 确保今天的日记文件存在且包含相应的部分标题
2. 脚本路径必须正确指向 js 文件
3. 如果遇到权限问题，检查 Obsidian 的脚本执行权限设置
4. **重要**：使用智能插入功能时，请确保当前打开的文件是今天的日记文件
5. 如果光标位置检测失败，脚本会自动回退到默认插入位置
6. 项目文件的状态必须设置为 "进行中" 或 "待办"
7. 任务必须使用 `#task` 标签标记

## 🔧 故障排除

### 常见问题

#### 1. 日期不匹配问题 ⭐ **已修复**
**症状**：脚本总是添加到错误的日期（如总是29号而不是30号）
**原因**：之前使用 `toISOString()` 获取UTC时间，存在时区偏差
**解决方案**：
- ✅ 已修复：现在使用本地时区获取日期
- 使用 `🔧 日期测试` 命令验证日期获取是否正确

#### 2. 找不到日记文件
**症状**：提示"今天的日记文件不存在"
**解决方案**：
- 确保日记文件路径正确：`🎓 study/📆 学习日志/日记/YYYY-MM-DD.md`
- 使用日记模板创建今天的日记文件
- 检查文件夹结构是否完整

#### 3. 找不到任务部分
**症状**：提示"未找到今日学习任务部分"
**解决方案**：
- 确保日记文件包含相关标题
- 支持的标题格式：
  - `### ✅ 今日学习任务`
  - `## 今日学习任务`
  - `📋 今日学习计划`
  - `今日学习计划`

#### 4. 项目任务不显示
**症状**：项目任务选择器中没有任务
**解决方案**：
- 确保项目文件在 `🎓 study/📌 学习项目` 文件夹中
- 确保项目状态设置为 "进行中" 或 "待办"
- 确保任务使用 `- [ ] #task 任务描述` 格式
- 检查项目文件是否包含 `状态: 进行中` 字段

### 调试工具

#### 🔧 日期测试命令
使用 `🔧 日期测试` 命令可以：
- ✅ 检查当前日期获取是否正确
- 📊 查看不同日期格式的对比
- 📁 确认日记文件路径是否存在
- 🕐 显示时区信息

#### 📝 控制台日志
脚本会在控制台输出调试信息：
1. 按 `Ctrl+Shift+I` 打开开发者工具
2. 查看 Console 标签页
3. 执行脚本时查看输出信息

### 联系支持
如果问题仍然存在，请：
1. 运行 `🔧 日期测试` 并截图结果
2. 检查控制台错误信息
3. 确认文件结构和格式是否正确
