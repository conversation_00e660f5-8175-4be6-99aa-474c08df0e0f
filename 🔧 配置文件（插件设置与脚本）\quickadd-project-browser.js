module.exports = async (params) => {
    const { quickAddApi: { suggester }, app } = params;
    
    try {
        // 获取所有进行中的项目
        const projectFolder = app.vault.getAbstractFileByPath("🎓 study/📌 学习项目");
        if (!projectFolder) {
            new Notice("学习项目文件夹不存在");
            return;
        }
        
        const projectFiles = [];
        const getAllFiles = (folder) => {
            for (const child of folder.children) {
                if (child.extension === 'md' && child.name !== '项目管理看板.md') {
                    projectFiles.push(child);
                }
                if (child.children) {
                    getAllFiles(child);
                }
            }
        };
        
        getAllFiles(projectFolder);
        
        if (projectFiles.length === 0) {
            new Notice("没有找到项目文件");
            return;
        }
        
        // 解析项目文件
        const projects = [];
        
        for (const file of projectFiles) {
            const content = await app.vault.read(file);
            const lines = content.split('\n');
            
            // 获取项目信息
            let status = '未知';
            let description = '';
            let taskCount = 0;
            let completedCount = 0;
            
            for (const line of lines) {
                if (line.includes('状态')) {
                    const statusMatch = line.match(/状态[：:]\s*(.+)/);
                    if (statusMatch) {
                        status = statusMatch[1].trim();
                    }
                }
                if (line.includes('描述') || line.includes('简介')) {
                    const descMatch = line.match(/[描述简介][：:]\s*(.+)/);
                    if (descMatch) {
                        description = descMatch[1].trim();
                    }
                }
                if (line.match(/^- \[ \]/)) {
                    taskCount++;
                }
                if (line.match(/^- \[x\]/)) {
                    completedCount++;
                }
            }
            
            const totalTasks = taskCount + completedCount;
            const progress = totalTasks > 0 ? Math.round((completedCount / totalTasks) * 100) : 0;
            
            projects.push({
                name: file.basename,
                file: file,
                status: status,
                description: description,
                taskCount: taskCount,
                completedCount: completedCount,
                progress: progress
            });
        }
        
        // 按状态和进度排序
        projects.sort((a, b) => {
            // 进行中的项目优先
            if (a.status === '进行中' && b.status !== '进行中') return -1;
            if (a.status !== '进行中' && b.status === '进行中') return 1;
            
            // 然后按未完成任务数量排序
            return b.taskCount - a.taskCount;
        });
        
        // 创建项目选择列表
        const projectOptions = projects.map(p => {
            const statusIcon = p.status === '进行中' ? '🟢' : 
                              p.status === '待办' ? '🟡' : 
                              p.status === '已完成' ? '✅' : '⚪';
            
            const progressBar = '█'.repeat(Math.floor(p.progress / 10)) + 
                               '░'.repeat(10 - Math.floor(p.progress / 10));
            
            return {
                display: `${statusIcon} ${p.name} [${p.progress}%] ${progressBar} (${p.taskCount}个待办任务)`,
                project: p
            };
        });
        
        // 让用户选择项目
        const selectedOption = await suggester(
            projectOptions.map(o => o.display),
            projectOptions,
            false,
            "选择要查看的项目："
        );
        
        if (!selectedOption) {
            new Notice("未选择项目");
            return;
        }
        
        const project = selectedOption.project;
        
        // 打开选中的项目文件
        const leaf = app.workspace.getLeaf(false);
        await leaf.openFile(project.file);
        
        // 显示项目摘要
        const summary = `
📊 项目摘要：
• 状态：${project.status}
• 进度：${project.progress}% (${project.completedCount}/${project.completedCount + project.taskCount})
• 待办任务：${project.taskCount}个
• 描述：${project.description || '无描述'}
        `.trim();
        
        new Notice(summary, 8000);
        
    } catch (error) {
        console.error('浏览项目时出错:', error);
        new Notice(`浏览项目失败: ${error.message}`);
    }
};
