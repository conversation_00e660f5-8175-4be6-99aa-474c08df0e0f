# 🎓 高效日程管理系统使用指南

## 🚀 快速开始

### 每日工作流程
1. **早晨（5分钟）**
   - 打开今日笔记（使用模板创建）
   - 查看项目任务推荐
   - 制定今日学习计划

2. **学习过程中**
   - 使用快捷键 `Ctrl+Shift+L` 快速记录事件日志
   - 或使用 `Ctrl+Shift+Q` 进行快速记录

3. **晚上（10分钟）**
   - 查看今日学习时长统计
   - 填写学习回顾
   - 规划明日重点

### 每周工作流程
1. **周日晚上**
   - 回顾本周项目进度
   - 调整下周学习重点
   - 更新项目看板状态

2. **周中检查**
   - 查看项目任务管理页面
   - 调整任务优先级
   - 处理阻塞问题

## 📋 核心功能使用

### 1. 事件日志记录
**方法一：详细记录**
- 命令：`📌 添加事件日志`
- 适用：重要学习活动的详细记录

**方法二：快速记录**
- 命令：`⚡ 快速记录`
- 格式：`14:30-15:20 数学习题练习`
- 适用：日常学习活动的快速记录

### 2. 项目任务管理
**添加项目任务到今日计划**
- 命令：`📌 添加项目任务到今日`
- 自动选择项目和任务
- 设置优先级

**项目状态管理**
- 在项目看板中拖拽卡片
- 自动更新项目文件状态
- 影响相关查询和统计

### 3. 数据查看和分析
**事件日志汇总**
- 文件：`🎓 study/📆 学习日志/事件日志汇总.md`
- 显示最近50条学习记录
- 包含每日时长统计

**学习时间线**
- 文件：`🎓 study/📆 学习日志/学习时间线视图.md`
- 今日时间线可视化
- 最近一周学习统计

**项目任务管理**
- 文件：`🎓 study/✅ 学习任务/项目任务管理.md`
- 所有项目概览
- 高优先级任务列表

## 🎯 最佳实践

### 标签使用规范
```
学科分类: #数学/高等数学 #英语/词汇
任务类型: #task/学习 #task/复习
优先级: #priority/high #priority/medium #priority/low
状态: #status/todo #status/doing #status/done
```

### 时间记录技巧
1. **及时记录**：学习结束后立即记录
2. **准确时间**：使用24小时制，精确到分钟
3. **简洁描述**：活动描述简洁明了
4. **真实感悟**：记录真实的学习感受

### 项目管理技巧
1. **合理分解**：将大项目分解为小任务
2. **设置里程碑**：关键节点设置检查点
3. **定期回顾**：每周回顾项目进度
4. **及时调整**：根据实际情况调整计划

## 🔧 故障排除

### 常见问题

**Q: 事件日志汇总显示不正确**
A: 检查日记文件是否在正确路径，确保事件日志格式正确

**Q: 项目任务不显示在每日笔记中**
A: 检查项目文件的标签和任务格式，确保包含 `#task` 标签

**Q: Kanban 拖拽不更新状态**
A: 检查项目文件是否包含 `状态` 字段，重启 Obsidian

**Q: QuickAdd 脚本不工作**
A: 检查脚本路径是否正确，确保今日日记文件存在

### 数据备份
1. **定期备份**：建议每周备份整个文件夹
2. **版本控制**：可以使用 Git 进行版本管理
3. **云同步**：使用 OneDrive、iCloud 等云服务同步

## 📈 进阶使用

### 自定义查询
学习 Dataview 语法，创建个性化的数据视图

### 脚本开发
基于现有脚本，开发符合个人需求的自动化功能

### 样式定制
通过 CSS 片段美化界面，提升使用体验

### 插件集成
探索更多插件，扩展系统功能

## 🎨 界面优化

### 推荐主题
- Minimal
- Blue Topaz
- AnuPpuccin

### 推荐字体
- 思源黑体
- 霞鹜文楷
- JetBrains Mono（代码）

### 快捷键设置
```
Ctrl+Shift+D - 创建今日笔记
Ctrl+Shift+P - 创建新项目  
Ctrl+Shift+T - 添加任务
Ctrl+Shift+L - 记录事件日志
Ctrl+Shift+Q - 快速记录
Ctrl+Shift+R - 打开回顾视图
```

## 📞 获取帮助

### 学习资源
- Obsidian 官方文档
- Dataview 插件文档
- Templater 插件文档
- 社区论坛和 Discord

### 问题反馈
如果遇到系统问题或有改进建议，可以：
1. 检查配置文件和脚本
2. 查看系统优化建议文档
3. 在社区寻求帮助

---

**记住：** 这个系统的价值在于持续使用和不断优化。开始时可能需要一些时间适应，但坚持使用会带来显著的效率提升！
