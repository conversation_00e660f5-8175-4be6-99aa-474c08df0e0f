# 📌 事件日志汇总

## 📊 最近事件日志概览

```dataviewjs
// 获取所有日记文件
const pages = dv.pages('"🎓 study/📆 学习日志/日记"')
  .where(p => p.file.name.match(/^\d{4}-\d{2}-\d{2}$/))
  .sort(p => p.file.name, 'desc');

// 创建表格数据
let tableData = [];

for (let page of pages) {
  const content = await dv.io.load(page.file.path);
  const lines = content.split('\n');

  // 查找事件日志条目
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    if (line.match(/^### 📌 \d{2}:\d{2} 事件日志$/)) {
      // 提取时间
      const timeMatch = line.match(/### 📌 (\d{2}:\d{2}) 事件日志/);
      const time = timeMatch ? timeMatch[1] : '';

      // 提取时间段、做了什么、感悟
      let timeRange = '';
      let activity = '';
      let thoughts = '';

      // 查找接下来的几行内容
      for (let j = i + 1; j < Math.min(i + 10, lines.length); j++) {
        const nextLine = lines[j];

        if (nextLine.includes('**时间段：**')) {
          const rangeMatch = nextLine.match(/`([^`]+)`\s*(\d+)min/);
          if (rangeMatch) {
            timeRange = `${rangeMatch[1]} (${rangeMatch[2]}分钟)`;
          }
        } else if (nextLine.includes('**做了什么：**')) {
          // 收集做了什么的内容
          for (let k = j + 1; k < lines.length; k++) {
            if (lines[k].includes('**当时想法与感悟：**')) break;
            if (lines[k].trim().startsWith('- ') || lines[k].trim().startsWith('\t- ')) {
              activity += lines[k].trim().replace(/^- /, '') + '; ';
            }
          }
        } else if (nextLine.includes('**当时想法与感悟：**')) {
          // 收集感悟内容
          for (let k = j + 1; k < lines.length; k++) {
            if (lines[k].startsWith('###') || lines[k].startsWith('##')) break;
            if (lines[k].trim().startsWith('- ') || lines[k].trim().startsWith('\t- ')) {
              thoughts += lines[k].trim().replace(/^- /, '') + '; ';
            }
          }
          break;
        }
      }

      tableData.push([
        `[[${page.file.name}|${page.file.name}]]`,
        time,
        timeRange,
        activity.trim(),
        thoughts.trim()
      ]);
    }
  }
}

// 显示表格
dv.table(
  ["日期", "时间", "时间段", "活动内容", "想法感悟"],
  tableData.slice(0, 50) // 限制显示最近50条记录
);
```

## 📈 每日学习时长统计

```dataviewjs
// 计算每日总学习时长
const pages = dv.pages('"🎓 study/📆 学习日志/日记"')
  .where(p => p.file.name.match(/^\d{4}-\d{2}-\d{2}$/))
  .sort(p => p.file.name, 'desc');

let dailyStats = [];

for (let page of pages.slice(0, 14)) { // 最近14天
  const content = await dv.io.load(page.file.path);
  const lines = content.split('\n');

  let totalMinutes = 0;
  let sessionCount = 0;

  for (let line of lines) {
    if (line.includes('**时间段：**')) {
      const minuteMatch = line.match(/(\d+)min/);
      if (minuteMatch) {
        totalMinutes += parseInt(minuteMatch[1]);
        sessionCount++;
      }
    }
  }

  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  const timeStr = hours > 0 ? `${hours}小时${minutes}分钟` : `${minutes}分钟`;

  dailyStats.push([
    `[[${page.file.name}|${page.file.name}]]`,
    sessionCount,
    timeStr,
    totalMinutes
  ]);
}

dv.table(
  ["日期", "学习次数", "总时长", "分钟数"],
  dailyStats
);
```

