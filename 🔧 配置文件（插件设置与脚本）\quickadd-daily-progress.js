module.exports = async (params) => {
    const { quickAddApi: { inputPrompt, suggester, yesNoPrompt }, app } = params;
    
    // 获取所有项目文件
    const projectFiles = app.vault.getMarkdownFiles()
        .filter(file => file.path.includes('🎓 study/📌 学习项目/') && !file.path.includes('项目管理看板'))
        .map(file => ({
            name: file.basename,
            path: file.path
        }));
    
    if (projectFiles.length === 0) {
        new Notice("未找到学习项目文件！");
        return;
    }
    
    // 选择项目
    const selectedProject = await suggester(
        projectFiles.map(p => p.name),
        projectFiles,
        false,
        "选择要记录进度的项目："
    );
    
    if (!selectedProject) return;
    
    // 获取今日日期
    const today = new Date();
    const dateStr = today.toISOString().split('T')[0];
    const timeStr = today.toTimeString().split(' ')[0].substring(0, 5);
    
    // 检查今日是否已有记录
    const dailyRecordPath = `🎓 study/📌 学习项目/每日记录/${selectedProject.name}/${dateStr}.md`;
    const existingFile = app.vault.getAbstractFileByPath(dailyRecordPath);
    
    if (existingFile) {
        const updateExisting = await yesNoPrompt("今日已有学习记录，是否更新？");
        if (!updateExisting) return;
    }
    
    // 收集学习数据
    const studyTime = await inputPrompt("学习时长（分钟）：", "60");
    const studyContent = await inputPrompt("今日学习内容：", "");
    const completionRate = await inputPrompt("今日完成度（0-100%）：", "80");
    const efficiencyScore = await inputPrompt("效率评分（1-10）：", "7");
    const difficultyScore = await inputPrompt("难度评分（1-10）：", "5");
    const studyHighlight = await inputPrompt("今日学习亮点：", "");
    const challenges = await inputPrompt("遇到的困难：", "");
    const tomorrowPlan = await inputPrompt("明日学习计划：", "");
    
    // 创建记录内容
    const recordContent = `---
日期: ${dateStr}
项目: "${selectedProject.name}"
学习时长: ${studyTime || 0}
完成度: ${completionRate || 0}
效率评分: ${efficiencyScore || 5}
难度评分: ${difficultyScore || 5}
tags:
  - 每日记录
  - 项目进度
  - "${selectedProject.name}"
---

# 📅 ${dateStr} - ${selectedProject.name} 学习记录

## ⏱️ 时间统计
- **学习时长**: ${studyTime || 0}分钟
- **记录时间**: ${timeStr}
- **有效学习时间**: ${studyTime || 0}分钟

## 📚 学习内容
### 今日学习主题
${studyContent || '待补充'}

### 完成的任务
- [ ] 主要学习任务
- [ ] 练习任务
- [ ] 复习任务

## 📈 进度评估
- **今日完成度**: ${completionRate || 0}%
- **效率评分**: ${efficiencyScore || 5}/10
- **难度评分**: ${difficultyScore || 5}/10

## 💡 学习收获
### 新掌握的知识点
${studyHighlight || '待补充'}

### 重要概念理解
待补充

## 🤔 问题与挑战
### 遇到的困难
${challenges || '无特别困难'}

### 解决方案
待补充

### 待解决问题
- [ ] 待解决问题1
- [ ] 待解决问题2

## 📝 明日计划
### 学习目标
${tomorrowPlan || '继续按计划学习'}

### 预计时长
60分钟

### 重点关注
待补充

## 🎯 反思总结
### 今日亮点
${studyHighlight || '按计划完成学习任务'}

### 改进建议
待补充

---
*记录时间: ${dateStr} ${timeStr}*`;

    // 确保目录存在
    const folderPath = `🎓 study/📌 学习项目/每日记录/${selectedProject.name}`;
    const folder = app.vault.getAbstractFileByPath(folderPath);
    if (!folder) {
        await app.vault.createFolder(folderPath);
    }
    
    // 创建或更新文件
    try {
        if (existingFile) {
            await app.vault.modify(existingFile, recordContent);
            new Notice(`已更新 ${selectedProject.name} 的今日学习记录！`);
        } else {
            await app.vault.create(dailyRecordPath, recordContent);
            new Notice(`已创建 ${selectedProject.name} 的今日学习记录！`);
        }
        
        // 打开创建的文件
        const file = app.vault.getAbstractFileByPath(dailyRecordPath);
        if (file) {
            app.workspace.getLeaf().openFile(file);
        }
        
    } catch (error) {
        new Notice(`创建记录失败: ${error.message}`);
        console.error("Daily progress record creation failed:", error);
    }
};
